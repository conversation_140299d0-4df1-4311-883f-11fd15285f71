@tailwind base;
@tailwind components;
@tailwind utilities;
 
/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--secondary) var(--primary);
}

 
 
@layer base {
  :root {
    /* MMORPG Light Theme (fallback) */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 214 100% 59%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 214 100% 59%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 214 100% 59%;
    --radius: 0.75rem;

    /* MMORPG specific colors */
    --game-primary: 214 100% 59%; /* Blue #3b82f6 */
    --game-secondary: 142 76% 36%; /* Green #16a34a */
    --game-warning: 38 92% 50%; /* Orange #ea580c */
    --game-danger: 0 84% 60%; /* Red #dc2626 */
    --game-success: 142 76% 36%; /* Green #16a34a */

    /* HUD colors */
    --hud-background: 0 0% 0% / 0.8;
    --hud-border: 0 0% 100% / 0.1;
    --hud-text: 0 0% 100%;

    /* XP Bar colors */
    --xp-background: 0 0% 20%;
    --xp-fill: 214 100% 59%;
    --xp-glow: 214 100% 59% / 0.5;
  }

  .dark {
    /* MMORPG Dark Theme (default) */
    --background: 222 84% 4.9%; /* Very dark blue-gray #0a0a0f */
    --foreground: 210 40% 98%; /* Light text */
    --card: 222 84% 4.9%; /* Dark cards */
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 214 100% 59%; /* Bright blue #3b82f6 */
    --primary-foreground: 222 84% 4.9%;
    --secondary: 217 32% 17.5%; /* Dark blue-gray #1e293b */
    --secondary-foreground: 210 40% 98%;
    --muted: 217 32% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217 32% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 32% 17.5%;
    --input: 217 32% 17.5%;
    --ring: 214 100% 59%;

    /* MMORPG specific dark colors */
    --game-primary: 214 100% 59%; /* Blue #3b82f6 */
    --game-secondary: 142 76% 36%; /* Green #16a34a */
    --game-warning: 38 92% 50%; /* Orange #ea580c */
    --game-danger: 0 84% 60%; /* Red #dc2626 */
    --game-success: 142 76% 36%; /* Green #16a34a */

    /* HUD colors for dark theme */
    --hud-background: 222 84% 4.9% / 0.95;
    --hud-border: 214 100% 59% / 0.2;
    --hud-text: 210 40% 98%;

    /* XP Bar colors for dark theme */
    --xp-background: 217 32% 17.5%;
    --xp-fill: 214 100% 59%;
    --xp-glow: 214 100% 59% / 0.3;
  }
  

 
}
 

* {
  scrollbar-color:  rgba(250, 250, 255, 0.1)  rgba(0, 0, 0, 0.2); /* Thumb color followed by track color */
}
 

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
 
 
 
/* MMORPG Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes bounceIn {
  0% { transform: scale(0.8) translateY(0); opacity: 0.9; }
  20% { transform: scale(1.05) translateY(-10px); opacity: 1; }
  40% { transform: scale(0.95) translateY(-5px); }
  60% { transform: scale(1.02) translateY(-3px); }
  80% { transform: scale(0.98) translateY(-1px); }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-20px); }
}

@keyframes slideInFromRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px hsl(var(--game-primary) / 0.5); }
  50% { box-shadow: 0 0 20px hsl(var(--game-primary) / 0.8), 0 0 30px hsl(var(--game-primary) / 0.6); }
}

@keyframes xpFill {
  from { width: 0%; }
  to { width: var(--xp-percentage, 0%); }
}

@keyframes levelUp {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); color: hsl(var(--game-success)); }
  100% { transform: scale(1); }
}

/* MMORPG Utility Classes */
.game-gradient {
  background: linear-gradient(135deg, hsl(var(--game-primary)) 0%, hsl(var(--game-secondary)) 100%);
}

.game-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.hud-panel {
  background: hsl(var(--hud-background));
  border: 1px solid hsl(var(--hud-border));
  backdrop-filter: blur(10px);
  border-radius: var(--radius);
}

.xp-bar {
  background: hsl(var(--xp-background));
  border-radius: 9999px;
  overflow: hidden;
  position: relative;
}

.xp-fill {
  background: linear-gradient(90deg, hsl(var(--xp-fill)), hsl(var(--game-secondary)));
  height: 100%;
  border-radius: 9999px;
  animation: xpFill 1s ease-out;
  box-shadow: 0 0 10px hsl(var(--xp-glow));
}

.level-badge {
  background: hsl(var(--game-primary));
  color: hsl(var(--primary-foreground));
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border: 2px solid hsl(var(--background));
}

.chat-message {
  animation: slideInFromLeft 0.3s ease-out;
  padding: 0.5rem;
  border-radius: var(--radius);
  margin-bottom: 0.25rem;
}

.chat-message.system {
  background: hsl(var(--muted) / 0.5);
  color: hsl(var(--muted-foreground));
  font-style: italic;
}

.chat-message.user {
  background: hsl(var(--card) / 0.8);
  border-left: 3px solid hsl(var(--game-primary));
}

.notification {
  animation: slideInFromRight 0.3s ease-out;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.notification.success {
  border-left: 4px solid hsl(var(--game-success));
}

.notification.warning {
  border-left: 4px solid hsl(var(--game-warning));
}

.notification.error {
  border-left: 4px solid hsl(var(--game-danger));
}