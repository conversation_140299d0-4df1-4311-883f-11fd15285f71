'use client'
import { useEffect, useRef, useState } from 'react'
import { Game } from '@/game/Game'
import GameHUD from '@/components/game/GameHUD'
import LoadingScreen from '@/components/LoadingScreen'
import AuthModal from '@/components/auth/AuthModal'
import { MessageComponent } from '@shared/component/MessageComponent'
import { GameInfo } from '@/types'
import { useAuth } from '@/lib/auth-context'

interface GamePlayerProps extends GameInfo {
  playerName?: string
}

export default function GamePlayer({ playerName, ...gameInfo }: GamePlayerProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [messages, setMessages] = useState<MessageComponent[]>([])
  const [gameInstance, setGameInstance] = useState<Game | null>(null)
  const [connectedPlayers, setConnectedPlayers] = useState(0)
  const [showAuthModal, setShowAuthModal] = useState(false)
  const refContainer = useRef(null)
  const { user, logout, isAuthenticated } = useAuth()

  // Convert MessageComponent to GameHUD message format
  const convertMessages = (messageComponents: MessageComponent[]) => {
    return messageComponents.map((msg, index) => ({
      id: `${msg.timestamp}-${index}`,
      username: msg.author,
      content: msg.content,
      timestamp: new Date(msg.timestamp),
      type: 'user' as const
    }))
  }

  const handleSendMessage = (message: string) => {
    if (gameInstance) {
      gameInstance.hud.sendMessageToServer(message)
    }
  }

  const handleLogout = () => {
    logout()
    setShowAuthModal(true)
  }

  useEffect(() => {
    async function initializeGame() {
      const game = Game.getInstance(gameInfo.websocketPort, refContainer)
      game.hud.passChatState(setMessages)
      setGameInstance(game)

      try {
        await game.start()

        // Set player name if provided
        if (playerName && playerName.trim()) {
          game.setPlayerName(playerName.trim())
        } else if (user?.username) {
          game.setPlayerName(user.username)
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Error connecting to WebSocket:', error)
        setIsLoading(false)
      }
    }

    initializeGame()
  }, [gameInfo.websocketPort, playerName, user?.username])

  // Show auth modal if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      setShowAuthModal(true)
    }
  }, [isAuthenticated])

  return (
    <div className="fixed inset-0 w-full h-full">
      {isLoading && <LoadingScreen />}

      {/* Authentication Modal */}
      {showAuthModal && (
        <AuthModal
          onClose={() => setShowAuthModal(false)}
          onSuccess={() => setShowAuthModal(false)}
        />
      )}

      {gameInstance && (
        <div ref={refContainer}>
          <GameHUD
            user={user}
            onLogout={handleLogout}
            onSendMessage={handleSendMessage}
            messages={convertMessages(messages)}
            connectedPlayers={connectedPlayers}
          />
        </div>
      )}
    </div>
  )
}
