'use client'

import { useEffect, useRef } from 'react'
import * as THREE from 'three'

interface GameEnvironmentProps {
  onEnvironmentReady?: (scene: THREE.Scene, camera: THREE.Camera, renderer: THREE.WebGLRenderer) => void
}

export default function GameEnvironment({ onEnvironmentReady }: GameEnvironmentProps) {
  const mountRef = useRef<HTMLDivElement>(null)
  const sceneRef = useRef<THREE.Scene | null>(null)
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null)
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null)

  useEffect(() => {
    if (!mountRef.current) return

    // Scene setup
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x87CEEB) // Sky blue
    sceneRef.current = scene

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    camera.position.set(0, 10, 20)
    camera.lookAt(0, 0, 0)
    cameraRef.current = camera

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
    rendererRef.current = renderer

    mountRef.current.appendChild(renderer.domElement)

    // Lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
    scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(50, 50, 50)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    scene.add(directionalLight)

    // Create Pet Simulator Environment
    createPetSimulatorEnvironment(scene)

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate)
      renderer.render(scene, camera)
    }
    animate()

    // Handle window resize
    const handleResize = () => {
      if (camera && renderer) {
        camera.aspect = window.innerWidth / window.innerHeight
        camera.updateProjectionMatrix()
        renderer.setSize(window.innerWidth, window.innerHeight)
      }
    }
    window.addEventListener('resize', handleResize)

    // Notify parent component
    if (onEnvironmentReady) {
      onEnvironmentReady(scene, camera, renderer)
    }

    return () => {
      window.removeEventListener('resize', handleResize)
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()
    }
  }, [onEnvironmentReady])

  return <div ref={mountRef} className="fixed inset-0 w-full h-full" />
}

function createPetSimulatorEnvironment(scene: THREE.Scene) {
  // Ground/Platform
  const groundGeometry = new THREE.PlaneGeometry(100, 100)
  const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 }) // Light green
  const ground = new THREE.Mesh(groundGeometry, groundMaterial)
  ground.rotation.x = -Math.PI / 2
  ground.receiveShadow = true
  scene.add(ground)

  // Central Play Area (where players collect coins)
  const playAreaGeometry = new THREE.CylinderGeometry(15, 15, 0.5, 32)
  const playAreaMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 }) // Gold
  const playArea = new THREE.Mesh(playAreaGeometry, playAreaMaterial)
  playArea.position.y = 0.25
  playArea.castShadow = true
  scene.add(playArea)

  // Egg Shop Building
  createEggShop(scene, { x: -25, y: 0, z: -25 })

  // Pet Display Area
  createPetDisplayArea(scene, { x: 25, y: 0, z: -25 })

  // Decorative Trees
  createTrees(scene)

  // Floating Coins (visual elements)
  createFloatingCoins(scene)

  // Spawn Points
  createSpawnPoints(scene)
}

function createEggShop(scene: THREE.Scene, position: { x: number, y: number, z: number }) {
  // Shop Building
  const shopGeometry = new THREE.BoxGeometry(8, 6, 8)
  const shopMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }) // Brown
  const shop = new THREE.Mesh(shopGeometry, shopMaterial)
  shop.position.set(position.x, position.y + 3, position.z)
  shop.castShadow = true
  scene.add(shop)

  // Shop Roof
  const roofGeometry = new THREE.ConeGeometry(6, 3, 4)
  const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 }) // Dark red
  const roof = new THREE.Mesh(roofGeometry, roofMaterial)
  roof.position.set(position.x, position.y + 7.5, position.z)
  roof.rotation.y = Math.PI / 4
  scene.add(roof)

  // Shop Sign
  const signGeometry = new THREE.PlaneGeometry(4, 1)
  const signMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF })
  const sign = new THREE.Mesh(signGeometry, signMaterial)
  sign.position.set(position.x, position.y + 8, position.z + 4.1)
  scene.add(sign)
}

function createPetDisplayArea(scene: THREE.Scene, position: { x: number, y: number, z: number }) {
  // Display Platform
  const platformGeometry = new THREE.CylinderGeometry(10, 10, 1, 16)
  const platformMaterial = new THREE.MeshLambertMaterial({ color: 0xDDA0DD }) // Plum
  const platform = new THREE.Mesh(platformGeometry, platformMaterial)
  platform.position.set(position.x, position.y + 0.5, position.z)
  platform.castShadow = true
  scene.add(platform)

  // Display Pedestals
  for (let i = 0; i < 5; i++) {
    const angle = (i / 5) * Math.PI * 2
    const x = position.x + Math.cos(angle) * 6
    const z = position.z + Math.sin(angle) * 6
    
    const pedestalGeometry = new THREE.CylinderGeometry(1, 1, 2, 8)
    const pedestalMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 }) // Silver
    const pedestal = new THREE.Mesh(pedestalGeometry, pedestalMaterial)
    pedestal.position.set(x, position.y + 2, z)
    scene.add(pedestal)
  }
}

function createTrees(scene: THREE.Scene) {
  const positions = [
    { x: -40, z: -40 }, { x: 40, z: -40 }, { x: -40, z: 40 }, { x: 40, z: 40 },
    { x: -30, z: 0 }, { x: 30, z: 0 }, { x: 0, z: -35 }, { x: 0, z: 35 }
  ]

  positions.forEach(pos => {
    // Tree trunk
    const trunkGeometry = new THREE.CylinderGeometry(1, 1.5, 8)
    const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 })
    const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial)
    trunk.position.set(pos.x, 4, pos.z)
    trunk.castShadow = true
    scene.add(trunk)

    // Tree leaves
    const leavesGeometry = new THREE.SphereGeometry(4, 8, 6)
    const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 })
    const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial)
    leaves.position.set(pos.x, 10, pos.z)
    leaves.castShadow = true
    scene.add(leaves)
  })
}

function createFloatingCoins(scene: THREE.Scene) {
  for (let i = 0; i < 20; i++) {
    const coinGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.1, 16)
    const coinMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 })
    const coin = new THREE.Mesh(coinGeometry, coinMaterial)
    
    coin.position.set(
      (Math.random() - 0.5) * 30,
      Math.random() * 5 + 2,
      (Math.random() - 0.5) * 30
    )
    
    coin.userData = { 
      rotationSpeed: Math.random() * 0.02 + 0.01,
      floatSpeed: Math.random() * 0.01 + 0.005,
      initialY: coin.position.y
    }
    
    scene.add(coin)
  }
}

function createSpawnPoints(scene: THREE.Scene) {
  const spawnPositions = [
    { x: -10, z: 10 }, { x: 10, z: 10 }, { x: -10, z: -10 }, { x: 10, z: -10 }
  ]

  spawnPositions.forEach((pos, index) => {
    const spawnGeometry = new THREE.RingGeometry(2, 3, 16)
    const spawnMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x00FFFF, 
      transparent: true, 
      opacity: 0.5 
    })
    const spawn = new THREE.Mesh(spawnGeometry, spawnMaterial)
    spawn.rotation.x = -Math.PI / 2
    spawn.position.set(pos.x, 0.1, pos.z)
    scene.add(spawn)
  })
}
