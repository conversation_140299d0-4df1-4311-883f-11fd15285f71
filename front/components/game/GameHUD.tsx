'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  MessageCircle, 
  Settings, 
  Users, 
  Trophy, 
  Coins, 
  LogOut,
  Send,
  X,
  Minimize2,
  Maximize2
} from 'lucide-react'

interface GameHUDProps {
  user?: {
    username: string
    profile?: {
      displayName: string | null
      level: number
      experience: number
      coins: number
    }
  }
  onLogout: () => void
  onSendMessage: (message: string) => void
  messages: Array<{
    id: string
    username: string
    content: string
    timestamp: Date
    type: 'user' | 'system'
  }>
  connectedPlayers: number
}

export default function GameHUD({ 
  user, 
  onLogout, 
  onSendMessage, 
  messages = [], 
  connectedPlayers = 0 
}: GameHUDProps) {
  const [chatOpen, setChatOpen] = useState(false)
  const [chatMinimized, setChatMinimized] = useState(false)
  const [messageInput, setMessageInput] = useState('')

  // Calculate XP progress for current level
  const getXPProgress = () => {
    if (!user?.profile) return { percentage: 0, current: 0, needed: 100 }
    
    const level = user.profile.level
    const currentXP = user.profile.experience
    const xpForCurrentLevel = (level - 1) * 100 // Simple formula: level 1 = 0 XP, level 2 = 100 XP, etc.
    const xpForNextLevel = level * 100
    const xpInCurrentLevel = currentXP - xpForCurrentLevel
    const xpNeededForLevel = xpForNextLevel - xpForCurrentLevel
    const percentage = Math.min((xpInCurrentLevel / xpNeededForLevel) * 100, 100)
    
    return {
      percentage,
      current: xpInCurrentLevel,
      needed: xpNeededForLevel
    }
  }

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (messageInput.trim()) {
      onSendMessage(messageInput.trim())
      setMessageInput('')
    }
  }

  const xpProgress = getXPProgress()

  return (
    <>
      {/* Top HUD Bar */}
      <div className="fixed top-4 left-4 right-4 z-40 pointer-events-none">
        <div className="flex justify-between items-start">
          {/* Player Info Panel */}
          <Card className="hud-panel pointer-events-auto nintendo-glow border-4 border-yellow-400 bg-gradient-to-br from-blue-800/95 to-purple-800/95">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-yellow-200 drop-shadow-md text-lg">
                      👑 {user?.profile?.displayName || user?.username || 'Guest'}
                    </span>
                    <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold border-2 border-white shadow-lg">
                      ⭐ Lv. {user?.profile?.level || 1}
                    </Badge>
                  </div>
                  
                  {/* XP Bar */}
                  <div className="mt-2 w-48">
                    <div className="flex justify-between text-xs text-yellow-200 mb-1 font-semibold">
                      <span>✨ Magic XP: {xpProgress.current}/{xpProgress.needed}</span>
                      <span>{Math.round(xpProgress.percentage)}%</span>
                    </div>
                    <div className="bg-purple-900 h-3 rounded-full border-2 border-yellow-400 overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 transition-all duration-1000 ease-out shadow-lg"
                        style={{ width: `${xpProgress.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>

                {/* Coins */}
                <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-400/90 to-orange-500/90 px-4 py-2 rounded-full border-2 border-white shadow-lg ml-4">
                  <Coins className="h-5 w-5 text-yellow-900 animate-pulse" />
                  <span className="font-bold text-yellow-900 text-lg">
                    {user?.profile?.coins || 0}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Server Info */}
          <Card className="hud-panel pointer-events-auto nintendo-glow border-4 border-green-400 bg-gradient-to-br from-green-800/95 to-blue-800/95">
            <CardContent className="p-3">
              <div className="flex items-center gap-2 text-sm">
                <Users className="h-5 w-5 text-green-300 animate-pulse" />
                <span className="text-green-200 font-bold">🌟 {connectedPlayers} heroes online</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Bottom Right Controls */}
      <div className="fixed bottom-4 right-4 z-40 flex flex-col gap-3 pointer-events-none">
        <Button
          onClick={() => setChatOpen(!chatOpen)}
          className="pointer-events-auto mario-button nintendo-glow"
          size="lg"
        >
          <MessageCircle className="h-5 w-5" />
          💬
        </Button>

        <Button
          onClick={onLogout}
          className="pointer-events-auto bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 text-white font-bold border-3 border-white rounded-2xl shadow-lg transform transition-all duration-150 hover:scale-105"
          size="lg"
        >
          <LogOut className="h-5 w-5" />
          🚪
        </Button>
      </div>

      {/* Chat Panel */}
      {chatOpen && (
        <div className="fixed bottom-20 right-4 z-50 w-80 pointer-events-auto">
          <Card className="hud-panel">
            <div className="flex items-center justify-between p-3 border-b border-hud-border">
              <h3 className="font-semibold text-hud-text">Global Chat</h3>
              <div className="flex gap-1">
                <Button
                  onClick={() => setChatMinimized(!chatMinimized)}
                  variant="ghost"
                  size="sm"
                >
                  {chatMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                </Button>
                <Button
                  onClick={() => setChatOpen(false)}
                  variant="ghost"
                  size="sm"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {!chatMinimized && (
              <>
                {/* Messages */}
                <div className="h-64 overflow-y-auto p-3 space-y-2">
                  {messages.length === 0 ? (
                    <p className="text-muted-foreground text-sm text-center py-8">
                      No messages yet. Start the conversation!
                    </p>
                  ) : (
                    messages.map((message) => (
                      <div
                        key={message.id}
                        className={`chat-message ${message.type}`}
                      >
                        <div className="flex items-start gap-2">
                          <span className="font-medium text-sm">
                            {message.username}:
                          </span>
                          <span className="text-sm flex-1">
                            {message.content}
                          </span>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Message Input */}
                <form onSubmit={handleSendMessage} className="p-3 border-t border-hud-border">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      placeholder="Type a message..."
                      className="flex-1 bg-background border border-input rounded-md px-3 py-2 text-sm"
                      maxLength={100}
                    />
                    <Button type="submit" size="sm" disabled={!messageInput.trim()}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </form>
              </>
            )}
          </Card>
        </div>
      )}

      {/* Notifications Area */}
      <div className="fixed top-20 right-4 z-30 space-y-2 pointer-events-none">
        {/* Notifications will be rendered here */}
      </div>
    </>
  )
}
