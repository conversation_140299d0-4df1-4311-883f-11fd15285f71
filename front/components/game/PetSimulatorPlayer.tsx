'use client'

import { useEffect, useRef, useState } from 'react'
import { Game } from '@/game/Game'
import GameEnvironment from '@/components/game/GameEnvironment'
import PetSimulatorHUD from '@/components/game/PetSimulatorHUD'
import LoadingScreen from '@/components/LoadingScreen'
import AuthModal from '@/components/auth/AuthModal'
import { MessageComponent } from '@shared/component/MessageComponent'
import { GameInfo } from '@/types'
import { useAuth } from '@/lib/auth-context'
import * as THREE from 'three'

interface Pet {
  id: string
  name: string
  rarity: 'Common' | 'Uncommon' | 'Rare' | 'Epic' | 'Legendary'
  level: number
  bonus: number
  emoji: string
}

interface PetSimulatorPlayerProps extends GameInfo {
  playerName?: string
}

export default function PetSimulatorPlayer({ playerName, ...gameInfo }: PetSimulatorPlayerProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [messages, setMessages] = useState<MessageComponent[]>([])
  const [gameInstance, setGameInstance] = useState<Game | null>(null)
  const [connectedPlayers, setConnectedPlayers] = useState(0)
  const [showAuthModal, setShowAuthModal] = useState(false)
  
  // Pet Simulator specific state
  const [playerCoins, setPlayerCoins] = useState(0)
  const [playerEggs, setPlayerEggs] = useState(0)
  const [pets, setPets] = useState<Pet[]>([])
  const [scene, setScene] = useState<THREE.Scene | null>(null)
  const [camera, setCamera] = useState<THREE.Camera | null>(null)
  const [renderer, setRenderer] = useState<THREE.WebGLRenderer | null>(null)
  
  const { user, logout, isAuthenticated } = useAuth()

  // Convert MessageComponent to HUD message format
  const convertMessages = (messageComponents: MessageComponent[]) => {
    return messageComponents.map((msg, index) => ({
      id: `${msg.timestamp}-${index}`,
      username: msg.author,
      content: msg.content,
      timestamp: new Date(msg.timestamp),
      type: 'user' as const
    }))
  }

  const handleSendMessage = (message: string) => {
    if (gameInstance) {
      gameInstance.hud.sendMessageToServer(message)
    }
  }

  const handleLogout = () => {
    logout()
    setShowAuthModal(true)
  }

  const handleEnvironmentReady = (newScene: THREE.Scene, newCamera: THREE.Camera, newRenderer: THREE.WebGLRenderer) => {
    setScene(newScene)
    setCamera(newCamera)
    setRenderer(newRenderer)
  }

  // Pet Simulator game logic
  const handleBuyEgg = (eggType: string) => {
    const eggPrices: Record<string, number> = {
      'Basic Egg': 100,
      'Spotted Egg': 350,
      'Golden Egg': 1000,
      'Crystal Egg': 2500
    }
    
    const price = eggPrices[eggType]
    if (playerCoins >= price) {
      setPlayerCoins(prev => prev - price)
      setPlayerEggs(prev => prev + 1)
      
      // Send message to other players
      handleSendMessage(`🥚 ${user?.username || 'Player'} bought a ${eggType}!`)
    }
  }

  const handleHatchEgg = () => {
    if (playerEggs > 0) {
      setPlayerEggs(prev => prev - 1)
      
      // Simple pet generation logic (this would be more complex in a real game)
      const petTypes = [
        { name: 'Cat', rarity: 'Common', bonus: 2, emoji: '🐱' },
        { name: 'Dog', rarity: 'Uncommon', bonus: 5, emoji: '🐶' },
        { name: 'Horse', rarity: 'Rare', bonus: 10, emoji: '🐴' },
        { name: 'Wolf', rarity: 'Epic', bonus: 15, emoji: '🐺' },
        { name: 'Golden Wolf', rarity: 'Legendary', bonus: 30, emoji: '🐺✨' }
      ]
      
      // Random pet selection (simplified)
      const randomPet = petTypes[Math.floor(Math.random() * petTypes.length)]
      const newPet: Pet = {
        id: Date.now().toString(),
        name: randomPet.name,
        rarity: randomPet.rarity as Pet['rarity'],
        level: 1,
        bonus: randomPet.bonus,
        emoji: randomPet.emoji
      }
      
      setPets(prev => [...prev, newPet])
      
      // Send message to other players
      const rarityEmoji = {
        Common: '⚪',
        Uncommon: '🟢',
        Rare: '🔵',
        Epic: '🟣',
        Legendary: '🟠'
      }
      
      handleSendMessage(`${rarityEmoji[newPet.rarity]} ${user?.username || 'Player'} hatched a ${newPet.rarity} ${newPet.name}! ${newPet.emoji}`)
      
      // Special announcement for legendary pets
      if (newPet.rarity === 'Legendary') {
        handleSendMessage(`🎉 LEGENDARY PET HATCHED! Everyone gets 50 bonus coins! 🎉`)
        setPlayerCoins(prev => prev + 50)
      }
    }
  }

  const handleCollectCoins = () => {
    const baseCoins = 10
    const petBonus = pets.reduce((total, pet) => total + pet.bonus, 0)
    const totalCoins = baseCoins + petBonus
    
    setPlayerCoins(prev => prev + totalCoins)
    
    // Random reward chance (15%)
    if (Math.random() < 0.15) {
      const rewards = [
        { type: 'coins', amount: 10, chance: 0.6 },
        { type: 'coins', amount: 50, chance: 0.2 },
        { type: 'coins', amount: 200, chance: 0.05 },
        { type: 'coins', amount: 1000, chance: 0.01 },
        { type: 'egg', amount: 1, chance: 0.14 }
      ]
      
      const random = Math.random()
      let cumulativeChance = 0
      
      for (const reward of rewards) {
        cumulativeChance += reward.chance
        if (random <= cumulativeChance) {
          if (reward.type === 'coins') {
            setPlayerCoins(prev => prev + reward.amount)
            handleSendMessage(`💰 ${user?.username || 'Player'} found ${reward.amount} bonus coins!`)
          } else if (reward.type === 'egg') {
            setPlayerEggs(prev => prev + reward.amount)
            handleSendMessage(`🥚 ${user?.username || 'Player'} found a mystery egg!`)
          }
          break
        }
      }
    }
  }

  // Passive income from pets
  useEffect(() => {
    const interval = setInterval(() => {
      const totalBonus = pets.reduce((total, pet) => total + pet.bonus, 0)
      if (totalBonus > 0) {
        setPlayerCoins(prev => prev + totalBonus)
      }
    }, 1000) // Every second

    return () => clearInterval(interval)
  }, [pets])

  useEffect(() => {
    async function initializeGame() {
      if (!scene || !camera || !renderer) return
      
      const game = Game.getInstance(gameInfo.websocketPort, { current: renderer.domElement.parentElement })
      game.hud.passChatState(setMessages)
      setGameInstance(game)
      
      try {
        await game.start()

        // Set player name if provided
        if (playerName && playerName.trim()) {
          game.setPlayerName(playerName.trim())
        } else if (user?.username) {
          game.setPlayerName(user.username)
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Error connecting to WebSocket:', error)
        setIsLoading(false)
      }
    }

    initializeGame()
  }, [gameInfo.websocketPort, playerName, user?.username, scene, camera, renderer])

  // Show auth modal if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      setShowAuthModal(true)
    }
  }, [isAuthenticated])

  // Give new players a starter egg
  useEffect(() => {
    if (isAuthenticated && playerEggs === 0 && pets.length === 0) {
      setPlayerEggs(1)
      setPlayerCoins(100) // Starting coins
    }
  }, [isAuthenticated, playerEggs, pets.length])

  return (
    <div className="fixed inset-0 w-full h-full">
      {isLoading && <LoadingScreen />}
      
      {/* Authentication Modal */}
      {showAuthModal && (
        <AuthModal 
          onClose={() => setShowAuthModal(false)}
          onSuccess={() => setShowAuthModal(false)}
        />
      )}
      
      {/* 3D Environment */}
      <GameEnvironment onEnvironmentReady={handleEnvironmentReady} />
      
      {/* Pet Simulator HUD */}
      <PetSimulatorHUD
        user={user}
        onLogout={handleLogout}
        onSendMessage={handleSendMessage}
        messages={convertMessages(messages)}
        connectedPlayers={connectedPlayers}
        playerCoins={playerCoins}
        playerEggs={playerEggs}
        pets={pets}
        onBuyEgg={handleBuyEgg}
        onHatchEgg={handleHatchEgg}
        onCollectCoins={handleCollectCoins}
      />
    </div>
  )
}
