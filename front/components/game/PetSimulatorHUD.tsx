'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { 
  MessageCircle, 
  Settings, 
  Users, 
  Trophy, 
  Coins, 
  LogOut,
  Send,
  X,
  Minimize2,
  Maximize2,
  ShoppingCart,
  Heart,
  Star,
  Gift,
  Zap
} from 'lucide-react'

interface Pet {
  id: string
  name: string
  rarity: 'Common' | 'Uncommon' | 'Rare' | 'Epic' | 'Legendary'
  level: number
  bonus: number
  emoji: string
}

interface PetSimulatorHUDProps {
  user?: {
    username: string
    profile?: {
      displayName: string | null
      level: number
      experience: number
      coins: number
    }
  }
  onLogout: () => void
  onSendMessage: (message: string) => void
  messages: Array<{
    id: string
    username: string
    content: string
    timestamp: Date
    type: 'user' | 'system'
  }>
  connectedPlayers: number
  // Pet Simulator specific props
  playerCoins: number
  playerEggs: number
  pets: Pet[]
  onBuyEgg: (eggType: string) => void
  onHatchEgg: () => void
  onCollectCoins: () => void
}

const EGG_TYPES = [
  { name: 'Basic Egg', price: 100, emoji: '🥚', description: 'Contains mostly common pets' },
  { name: 'Spotted Egg', price: 350, emoji: '🥚🟢', description: 'Higher chance for uncommon and rare' },
  { name: 'Golden Egg', price: 1000, emoji: '🥚✨', description: 'Much higher chance for rare and epic' },
  { name: 'Crystal Egg', price: 2500, emoji: '🥚💎', description: 'High chance for epic and legendary' }
]

const RARITY_COLORS = {
  Common: '#AAAAAA',
  Uncommon: '#55AA55',
  Rare: '#5555FF',
  Epic: '#AA00AA',
  Legendary: '#FFAA00'
}

export default function PetSimulatorHUD({ 
  user, 
  onLogout, 
  onSendMessage, 
  messages = [], 
  connectedPlayers = 0,
  playerCoins = 0,
  playerEggs = 0,
  pets = [],
  onBuyEgg,
  onHatchEgg,
  onCollectCoins
}: PetSimulatorHUDProps) {
  const [chatOpen, setChatOpen] = useState(false)
  const [shopOpen, setShopOpen] = useState(false)
  const [petsOpen, setPetsOpen] = useState(false)
  const [messageInput, setMessageInput] = useState('')

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (messageInput.trim()) {
      onSendMessage(messageInput.trim())
      setMessageInput('')
    }
  }

  const getTotalPetBonus = () => {
    return pets.reduce((total, pet) => total + pet.bonus, 0)
  }

  const getPetsByRarity = () => {
    const grouped: Record<string, Pet[]> = {}
    pets.forEach(pet => {
      if (!grouped[pet.rarity]) grouped[pet.rarity] = []
      grouped[pet.rarity].push(pet)
    })
    return grouped
  }

  return (
    <>
      {/* Top HUD Bar */}
      <div className="fixed top-4 left-4 right-4 z-40 pointer-events-none">
        <div className="flex justify-between items-start">
          {/* Player Info Panel */}
          <Card className="hud-panel pointer-events-auto nintendo-glow border-4 border-pink-400 bg-gradient-to-br from-pink-800/95 to-purple-800/95">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-pink-200 drop-shadow-md text-lg">
                      🐾 {user?.profile?.displayName || user?.username || 'Guest'}
                    </span>
                    <Badge className="bg-gradient-to-r from-pink-400 to-purple-500 text-white font-bold border-2 border-white shadow-lg">
                      ⭐ Lv. {user?.profile?.level || 1}
                    </Badge>
                  </div>
                  
                  {/* Pet Simulator Stats */}
                  <div className="mt-2 flex gap-4 text-sm">
                    <div className="flex items-center gap-1 bg-yellow-400/20 px-2 py-1 rounded-full border border-yellow-400">
                      <Coins className="h-4 w-4 text-yellow-300 animate-pulse" />
                      <span className="font-bold text-yellow-200">{playerCoins}</span>
                    </div>
                    <div className="flex items-center gap-1 bg-purple-400/20 px-2 py-1 rounded-full border border-purple-400">
                      <span className="text-lg">🥚</span>
                      <span className="font-bold text-purple-200">{playerEggs}</span>
                    </div>
                    <div className="flex items-center gap-1 bg-pink-400/20 px-2 py-1 rounded-full border border-pink-400">
                      <Heart className="h-4 w-4 text-pink-300 animate-pulse" />
                      <span className="font-bold text-pink-200">{pets.length}</span>
                    </div>
                    <div className="flex items-center gap-1 bg-blue-400/20 px-2 py-1 rounded-full border border-blue-400">
                      <Zap className="h-4 w-4 text-blue-300" />
                      <span className="font-bold text-blue-200">+{getTotalPetBonus()}/s ⚡</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Server Info */}
          <Card className="hud-panel pointer-events-auto">
            <CardContent className="p-3">
              <div className="flex items-center gap-2 text-sm">
                <Users className="h-4 w-4 text-green-500" />
                <span className="text-hud-text">{connectedPlayers} online</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Game Action Buttons */}
      <div className="fixed bottom-4 left-4 z-40 flex flex-col gap-2 pointer-events-none">
        <Button
          onClick={onCollectCoins}
          className="pointer-events-auto game-glow bg-yellow-600 hover:bg-yellow-500"
          size="lg"
        >
          <Coins className="h-5 w-5 mr-2" />
          Collect Coins
        </Button>
        
        {playerEggs > 0 && (
          <Button
            onClick={onHatchEgg}
            className="pointer-events-auto game-glow bg-purple-600 hover:bg-purple-500"
            size="lg"
          >
            <span className="text-lg mr-2">🥚</span>
            Hatch Egg
          </Button>
        )}
      </div>

      {/* Right Side Controls */}
      <div className="fixed bottom-4 right-4 z-40 flex flex-col gap-2 pointer-events-none">
        <Button
          onClick={() => setPetsOpen(!petsOpen)}
          className="pointer-events-auto game-glow bg-pink-600 hover:bg-pink-500"
          size="lg"
        >
          <Heart className="h-5 w-5" />
        </Button>
        
        <Button
          onClick={() => setShopOpen(!shopOpen)}
          className="pointer-events-auto game-glow bg-green-600 hover:bg-green-500"
          size="lg"
        >
          <ShoppingCart className="h-5 w-5" />
        </Button>
        
        <Button
          onClick={() => setChatOpen(!chatOpen)}
          className="pointer-events-auto game-glow"
          size="lg"
        >
          <MessageCircle className="h-5 w-5" />
        </Button>
        
        <Button
          onClick={onLogout}
          variant="outline"
          className="pointer-events-auto"
          size="lg"
        >
          <LogOut className="h-5 w-5" />
        </Button>
      </div>

      {/* Shop Panel */}
      {shopOpen && (
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-96 pointer-events-auto">
          <Card className="hud-panel">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-hud-text">🥚 Egg Shop</CardTitle>
              <Button
                onClick={() => setShopOpen(false)}
                variant="ghost"
                size="sm"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-3">
              {EGG_TYPES.map((egg) => (
                <div key={egg.name} className="flex items-center justify-between p-3 bg-background/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{egg.emoji}</span>
                    <div>
                      <div className="font-medium">{egg.name}</div>
                      <div className="text-sm text-muted-foreground">{egg.description}</div>
                    </div>
                  </div>
                  <Button
                    onClick={() => onBuyEgg(egg.name)}
                    disabled={playerCoins < egg.price}
                    size="sm"
                    className="bg-yellow-600 hover:bg-yellow-500"
                  >
                    <Coins className="h-4 w-4 mr-1" />
                    {egg.price}
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Pets Panel */}
      {petsOpen && (
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-96 max-h-96 pointer-events-auto">
          <Card className="hud-panel">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-hud-text">🐾 My Pets</CardTitle>
              <Button
                onClick={() => setPetsOpen(false)}
                variant="ghost"
                size="sm"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="max-h-64 overflow-y-auto">
              {pets.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  No pets yet! Buy an egg to get started.
                </p>
              ) : (
                <div className="space-y-2">
                  {Object.entries(getPetsByRarity()).map(([rarity, rarityPets]) => (
                    <div key={rarity}>
                      <h4 className="font-medium mb-2" style={{ color: RARITY_COLORS[rarity as keyof typeof RARITY_COLORS] }}>
                        {rarity} ({rarityPets.length})
                      </h4>
                      {rarityPets.map((pet) => (
                        <div key={pet.id} className="flex items-center justify-between p-2 bg-background/30 rounded">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{pet.emoji}</span>
                            <div>
                              <div className="font-medium">{pet.name}</div>
                              <div className="text-sm text-muted-foreground">Level {pet.level}</div>
                            </div>
                          </div>
                          <div className="text-sm font-medium">+{pet.bonus}/s</div>
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Chat Panel */}
      {chatOpen && (
        <div className="fixed bottom-20 right-4 z-50 w-80 pointer-events-auto">
          <Card className="hud-panel">
            <div className="flex items-center justify-between p-3 border-b border-hud-border">
              <h3 className="font-semibold text-hud-text">Global Chat</h3>
              <Button
                onClick={() => setChatOpen(false)}
                variant="ghost"
                size="sm"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="h-64 overflow-y-auto p-3 space-y-2">
              {messages.length === 0 ? (
                <p className="text-muted-foreground text-sm text-center py-8">
                  No messages yet. Start the conversation!
                </p>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`chat-message ${message.type}`}
                  >
                    <div className="flex items-start gap-2">
                      <span className="font-medium text-sm">
                        {message.username}:
                      </span>
                      <span className="text-sm flex-1">
                        {message.content}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>

            <form onSubmit={handleSendMessage} className="p-3 border-t border-hud-border">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 bg-background border border-input rounded-md px-3 py-2 text-sm"
                  maxLength={100}
                />
                <Button type="submit" size="sm" disabled={!messageInput.trim()}>
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </form>
          </Card>
        </div>
      )}
    </>
  )
}
