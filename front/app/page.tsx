'use client'

import { useState } from 'react'
import GameCard from '@/components/GameCard'
import KeyboardLayout from '@/components/KeyboardLayout'
import Navbar from '@/components/Navbar'
import AuthModal from '@/components/auth/AuthModal'
import { useAuth } from '@/lib/auth-context'
import { Button } from '@/components/ui/button'
import { ExternalLink, Github, Twitter, LogIn, UserPlus } from 'lucide-react'
import Link from 'next/link'
import { GameInfo } from '../types'
import gameData from '../public/gameData.json'

export default function Home() {
  const { user, login, logout } = useAuth()
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const games = gameData as GameInfo[]

  const handleAuthSuccess = (userData: any, token: string) => {
    login(userData, token)
    setAuthModalOpen(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted">
      <div className="space-y-8 flex flex-col items-center px-4 container">
        <Navbar />

        {/* Hero Section */}
        <div className="text-center space-y-6 py-12">
          <h1 className="text-4xl md:text-6xl font-bold game-gradient bg-clip-text text-transparent">
            Welcome to Lexialia
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Experience the next generation of browser-based MMORPG gaming.
            Join thousands of players in persistent worlds with real-time physics and social features.
          </p>

          {/* Authentication Buttons */}
          {!user ? (
            <div className="flex gap-4 justify-center">
              <Button
                onClick={() => setAuthModalOpen(true)}
                className="game-gradient game-glow"
                size="lg"
              >
                <LogIn className="mr-2 h-5 w-5" />
                Login
              </Button>
              <Button
                onClick={() => setAuthModalOpen(true)}
                variant="outline"
                size="lg"
              >
                <UserPlus className="mr-2 h-5 w-5" />
                Create Account
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center gap-4">
              <div className="text-lg">
                Welcome back, <span className="font-semibold text-primary">{user.profile?.displayName || user.username}</span>!
              </div>
              <div className="flex gap-4">
                <Button
                  onClick={logout}
                  variant="outline"
                >
                  Logout
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Games Grid */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-6 text-center">Available Worlds</h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {games &&
              games.map((game, index) => (
                <div key={index} className="transform transition-transform hover:scale-105">
                  <GameCard {...game} />
                </div>
              ))}
          </div>
        </div>

        <KeyboardLayout />

        {/* Social Links */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3 my-8 w-full max-w-2xl">
          <Link
            href={'https://discord.gg/kPhgtj49U2'}
            className="flex py-3 items-center justify-center px-6 font-medium border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            Project Discord
          </Link>
          <Link
            href={'https://twitter.com/iErcan_'}
            className="flex py-3 items-center justify-center px-6 font-medium border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            <Twitter className="mr-2 h-4 w-4" />
            Twitter
          </Link>
          <Link
            href={'https://github.com/iErcann/Notblox'}
            className="flex py-3 items-center justify-center px-6 font-medium border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            <Github className="mr-2 h-4 w-4" />
            Source Code
          </Link>
        </div>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        onSuccess={handleAuthSuccess}
      />
    </div>
  )
}
