'use client'

import { useState } from 'react'
import GameCard from '@/components/GameCard'
import KeyboardLayout from '@/components/KeyboardLayout'
import Navbar from '@/components/Navbar'
import AuthModal from '@/components/auth/AuthModal'
import { useAuth } from '@/lib/auth-context'
import { Button } from '@/components/ui/button'
import { ExternalLink, Github, Twitter, LogIn, UserPlus } from 'lucide-react'
import Link from 'next/link'
import { GameInfo } from '../types'
import gameData from '../public/gameData.json'

export default function Home() {
  const { user, login, logout } = useAuth()
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const games = gameData as GameInfo[]

  const handleAuthSuccess = (userData: any, token: string) => {
    login(userData, token)
    setAuthModalOpen(false)
  }

  return (
    <div className="min-h-screen nintendo-gradient relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-10 w-16 h-16 bg-yellow-300 rounded-full opacity-70 animate-bounce"></div>
        <div className="absolute top-32 right-20 w-12 h-12 bg-pink-300 rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute bottom-20 left-1/4 w-20 h-20 bg-green-300 rounded-full opacity-50 animate-bounce" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 right-10 w-14 h-14 bg-blue-300 rounded-full opacity-60 animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-32 right-1/3 w-18 h-18 bg-purple-300 rounded-full opacity-70 animate-bounce" style={{animationDelay: '0.5s'}}></div>
      </div>

      <div className="relative z-10 space-y-8 flex flex-col items-center px-4 container">
        <Navbar />

        {/* Hero Section */}
        <div className="text-center space-y-6 py-12">
          <h1 className="text-4xl md:text-6xl font-bold text-white drop-shadow-lg nintendo-glow">
            🏰 Welcome to Lexialia ✨
          </h1>
          <p className="text-xl text-white/90 max-w-2xl mx-auto drop-shadow-md">
            🌟 Embark on magical adventures in our enchanted multiplayer world! 🗡️ Collect treasures,
            battle mystical creatures, and forge friendships in this colorful medieval realm! 🏰✨
          </p>

          {/* Authentication Buttons */}
          {!user ? (
            <div className="flex gap-6 justify-center">
              <Button
                onClick={() => setAuthModalOpen(true)}
                className="mario-button text-white font-bold text-lg px-8 py-4 nintendo-glow"
                size="lg"
              >
                <LogIn className="mr-2 h-5 w-5" />
                🎮 Login
              </Button>
              <Button
                onClick={() => setAuthModalOpen(true)}
                className="bg-gradient-to-r from-green-400 to-green-600 hover:from-green-500 hover:to-green-700 text-white font-bold text-lg px-8 py-4 border-3 border-white rounded-3xl shadow-lg transform transition-all duration-150 hover:scale-105"
                size="lg"
              >
                <UserPlus className="mr-2 h-5 w-5" />
                ⭐ Create Account
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center gap-4">
              <div className="text-lg">
                Welcome back, <span className="font-semibold text-primary">{user.profile?.displayName || user.username}</span>!
              </div>
              <div className="flex gap-4">
                <Button
                  onClick={logout}
                  variant="outline"
                >
                  Logout
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Games Grid */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-6 text-center">Available Worlds</h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {games &&
              games.map((game, index) => (
                <div key={index} className="transform transition-transform hover:scale-105">
                  <GameCard {...game} />
                </div>
              ))}
          </div>
        </div>

        <KeyboardLayout />

        {/* Social Links */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3 my-8 w-full max-w-2xl">
          <Link
            href={'https://discord.gg/kPhgtj49U2'}
            className="flex py-3 items-center justify-center px-6 font-medium border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            Project Discord
          </Link>
          <Link
            href={'https://twitter.com/iErcan_'}
            className="flex py-3 items-center justify-center px-6 font-medium border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            <Twitter className="mr-2 h-4 w-4" />
            Twitter
          </Link>
          <Link
            href={'https://github.com/iErcann/Notblox'}
            className="flex py-3 items-center justify-center px-6 font-medium border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            <Github className="mr-2 h-4 w-4" />
            Source Code
          </Link>
        </div>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        onSuccess={handleAuthSuccess}
      />
    </div>
  )
}
