{"name": "lexialia-mmorpg", "version": "2.0.0", "description": "A modern web-based MMORPG with real-time multiplayer, persistent progression, and immersive 3D gameplay", "main": "index.js", "scripts": {"dev": "concurrently \"cd back && npm run dev\" \"cd front && npm run dev\"", "build": "cd back && npm run build && cd ../front && npm run build", "start": "concurrently \"cd back && npm start\" \"cd front && npm start\"", "test": "cd back && npm test && cd ../front && npm test", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm test && npm run test:e2e", "lint": "cd back && npm run lint && cd ../front && npm run lint", "format": "cd back && npm run format && cd ../front && npm run format", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "cd back && npm install && cd ../front && npm install && npm install", "clean": "cd back && rm -rf node_modules dist && cd ../front && rm -rf node_modules .next && rm -rf node_modules"}, "keywords": ["mmorpg", "multiplayer", "game", "threejs", "nodejs", "typescript", "websocket"], "author": "Lexialia Team", "license": "ISC", "devDependencies": {"@playwright/test": "^1.40.0", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}