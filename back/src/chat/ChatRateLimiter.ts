export interface RateLimitConfig {
  maxMessages: number
  windowMs: number
  cooldownMs?: number
}

export interface RateLimitResult {
  allowed: boolean
  remainingMessages?: number
  resetTime?: number
  error?: string
}

export class ChatRateLimiter {
  private userMessageCounts: Map<string, { count: number; windowStart: number; lastMessage: number }> = new Map()
  private config: RateLimitConfig

  constructor(config: RateLimitConfig = { maxMessages: 5, windowMs: 60000, cooldownMs: 1000 }) {
    this.config = config
    
    // Clean up old entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000)
  }

  /**
   * Check if user is allowed to send a message
   */
  checkRateLimit(userId: string): RateLimitResult {
    const now = Date.now()
    const userKey = userId || 'anonymous'
    
    // Get or create user data
    let userData = this.userMessageCounts.get(userKey)
    if (!userData) {
      userData = { count: 0, windowStart: now, lastMessage: 0 }
      this.userMessageCounts.set(userKey, userData)
    }

    // Check cooldown period
    if (this.config.cooldownMs && now - userData.lastMessage < this.config.cooldownMs) {
      return {
        allowed: false,
        error: `Please wait ${Math.ceil((this.config.cooldownMs - (now - userData.lastMessage)) / 1000)} seconds before sending another message`
      }
    }

    // Check if we need to reset the window
    if (now - userData.windowStart >= this.config.windowMs) {
      userData.count = 0
      userData.windowStart = now
    }

    // Check if user has exceeded the limit
    if (userData.count >= this.config.maxMessages) {
      const resetTime = userData.windowStart + this.config.windowMs
      const waitTime = Math.ceil((resetTime - now) / 1000)
      
      return {
        allowed: false,
        resetTime,
        error: `Rate limit exceeded. Please wait ${waitTime} seconds before sending another message`
      }
    }

    // Allow the message
    userData.count++
    userData.lastMessage = now
    
    return {
      allowed: true,
      remainingMessages: this.config.maxMessages - userData.count,
      resetTime: userData.windowStart + this.config.windowMs
    }
  }

  /**
   * Get current rate limit status for a user
   */
  getRateLimitStatus(userId: string): { count: number; limit: number; resetTime: number; remainingMessages: number } {
    const now = Date.now()
    const userKey = userId || 'anonymous'
    const userData = this.userMessageCounts.get(userKey)

    if (!userData) {
      return {
        count: 0,
        limit: this.config.maxMessages,
        resetTime: now + this.config.windowMs,
        remainingMessages: this.config.maxMessages
      }
    }

    // Check if window has expired
    if (now - userData.windowStart >= this.config.windowMs) {
      return {
        count: 0,
        limit: this.config.maxMessages,
        resetTime: now + this.config.windowMs,
        remainingMessages: this.config.maxMessages
      }
    }

    return {
      count: userData.count,
      limit: this.config.maxMessages,
      resetTime: userData.windowStart + this.config.windowMs,
      remainingMessages: Math.max(0, this.config.maxMessages - userData.count)
    }
  }

  /**
   * Reset rate limit for a user (admin function)
   */
  resetUserRateLimit(userId: string): boolean {
    const userKey = userId || 'anonymous'
    return this.userMessageCounts.delete(userKey)
  }

  /**
   * Update rate limit configuration
   */
  updateConfig(newConfig: Partial<RateLimitConfig>) {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Get current configuration
   */
  getConfig(): RateLimitConfig {
    return { ...this.config }
  }

  /**
   * Clean up old entries
   */
  private cleanup() {
    const now = Date.now()
    const expiredThreshold = this.config.windowMs * 2 // Keep data for 2 windows

    for (const [userId, userData] of this.userMessageCounts.entries()) {
      if (now - userData.windowStart > expiredThreshold) {
        this.userMessageCounts.delete(userId)
      }
    }
  }

  /**
   * Get statistics about rate limiting
   */
  getStats() {
    const now = Date.now()
    let activeUsers = 0
    let totalMessages = 0
    let rateLimitedUsers = 0

    for (const [userId, userData] of this.userMessageCounts.entries()) {
      // Only count users within the current window
      if (now - userData.windowStart < this.config.windowMs) {
        activeUsers++
        totalMessages += userData.count
        
        if (userData.count >= this.config.maxMessages) {
          rateLimitedUsers++
        }
      }
    }

    return {
      activeUsers,
      totalMessages,
      rateLimitedUsers,
      config: this.config
    }
  }

  /**
   * Check if a user is currently rate limited
   */
  isUserRateLimited(userId: string): boolean {
    const result = this.checkRateLimit(userId)
    // Revert the count increment since this is just a check
    if (result.allowed) {
      const userKey = userId || 'anonymous'
      const userData = this.userMessageCounts.get(userKey)
      if (userData) {
        userData.count--
      }
    }
    return !result.allowed
  }
}
