import { prisma } from '../database/prisma.js'

export interface ChatMessage {
  id: string
  roomId: string
  userId: string | null
  username: string
  content: string
  messageType: 'user' | 'system' | 'admin'
  createdAt: Date
}

export interface ChatMessageInput {
  roomId: string
  userId?: string
  username: string
  content: string
  messageType?: 'user' | 'system' | 'admin'
}

export class ChatService {
  private static readonly MAX_MESSAGE_LENGTH = 200
  private static readonly MAX_MESSAGES_PER_ROOM = 100

  /**
   * Save a chat message to the database
   */
  static async saveMessage(messageData: ChatMessageInput): Promise<ChatMessage | null> {
    try {
      // Validate message content
      if (!messageData.content || messageData.content.trim().length === 0) {
        return null
      }

      // Truncate message if too long
      const content = messageData.content.trim().substring(0, this.MAX_MESSAGE_LENGTH)

      const message = await prisma.chatMessage.create({
        data: {
          roomId: messageData.roomId,
          userId: messageData.userId || null,
          username: messageData.username,
          content,
          messageType: messageData.messageType || 'user'
        }
      })

      // Clean up old messages to keep room history manageable
      await this.cleanupOldMessages(messageData.roomId)

      return {
        id: message.id,
        roomId: message.roomId,
        userId: message.userId,
        username: message.username,
        content: message.content,
        messageType: message.messageType as 'user' | 'system' | 'admin',
        createdAt: message.createdAt
      }
    } catch (error) {
      console.error('Error saving chat message:', error)
      return null
    }
  }

  /**
   * Get recent messages for a room
   */
  static async getRecentMessages(roomId: string, limit: number = 50): Promise<ChatMessage[]> {
    try {
      const messages = await prisma.chatMessage.findMany({
        where: { roomId },
        orderBy: { createdAt: 'desc' },
        take: limit
      })

      return messages.reverse().map(message => ({
        id: message.id,
        roomId: message.roomId,
        userId: message.userId,
        username: message.username,
        content: message.content,
        messageType: message.messageType as 'user' | 'system' | 'admin',
        createdAt: message.createdAt
      }))
    } catch (error) {
      console.error('Error getting recent messages:', error)
      return []
    }
  }

  /**
   * Clean up old messages to keep room history manageable
   */
  private static async cleanupOldMessages(roomId: string) {
    try {
      const messageCount = await prisma.chatMessage.count({
        where: { roomId }
      })

      if (messageCount > this.MAX_MESSAGES_PER_ROOM) {
        const messagesToDelete = messageCount - this.MAX_MESSAGES_PER_ROOM
        
        const oldMessages = await prisma.chatMessage.findMany({
          where: { roomId },
          orderBy: { createdAt: 'asc' },
          take: messagesToDelete,
          select: { id: true }
        })

        if (oldMessages.length > 0) {
          await prisma.chatMessage.deleteMany({
            where: {
              id: { in: oldMessages.map(m => m.id) }
            }
          })
        }
      }
    } catch (error) {
      console.error('Error cleaning up old messages:', error)
    }
  }

  /**
   * Get message statistics for a room
   */
  static async getRoomChatStats(roomId: string) {
    try {
      const totalMessages = await prisma.chatMessage.count({
        where: { roomId }
      })

      const userMessages = await prisma.chatMessage.count({
        where: { 
          roomId,
          messageType: 'user'
        }
      })

      const systemMessages = await prisma.chatMessage.count({
        where: { 
          roomId,
          messageType: 'system'
        }
      })

      const recentActivity = await prisma.chatMessage.findFirst({
        where: { roomId },
        orderBy: { createdAt: 'desc' },
        select: { createdAt: true }
      })

      return {
        totalMessages,
        userMessages,
        systemMessages,
        lastActivity: recentActivity?.createdAt || null
      }
    } catch (error) {
      console.error('Error getting room chat stats:', error)
      return null
    }
  }

  /**
   * Get top chatters for a room
   */
  static async getTopChatters(roomId: string, limit: number = 10) {
    try {
      const result = await prisma.chatMessage.groupBy({
        by: ['username'],
        where: {
          roomId,
          messageType: 'user'
        },
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: limit
      })

      return result.map(item => ({
        username: item.username,
        messageCount: item._count.id
      }))
    } catch (error) {
      console.error('Error getting top chatters:', error)
      return []
    }
  }

  /**
   * Search messages in a room
   */
  static async searchMessages(roomId: string, query: string, limit: number = 20): Promise<ChatMessage[]> {
    try {
      const messages = await prisma.chatMessage.findMany({
        where: {
          roomId,
          content: {
            contains: query,
            mode: 'insensitive'
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit
      })

      return messages.map(message => ({
        id: message.id,
        roomId: message.roomId,
        userId: message.userId,
        username: message.username,
        content: message.content,
        messageType: message.messageType as 'user' | 'system' | 'admin',
        createdAt: message.createdAt
      }))
    } catch (error) {
      console.error('Error searching messages:', error)
      return []
    }
  }

  /**
   * Delete a message (admin function)
   */
  static async deleteMessage(messageId: string, adminUserId: string): Promise<boolean> {
    try {
      await prisma.chatMessage.delete({
        where: { id: messageId }
      })

      // Log the deletion
      console.log(`Message ${messageId} deleted by admin ${adminUserId}`)
      return true
    } catch (error) {
      console.error('Error deleting message:', error)
      return false
    }
  }

  /**
   * Validate message content
   */
  static validateMessage(content: string): { valid: boolean, error?: string } {
    if (!content || content.trim().length === 0) {
      return { valid: false, error: 'Message cannot be empty' }
    }

    if (content.length > this.MAX_MESSAGE_LENGTH) {
      return { valid: false, error: `Message too long (max ${this.MAX_MESSAGE_LENGTH} characters)` }
    }

    // Basic profanity filter (you might want to use a more sophisticated solution)
    const profanityWords = ['spam', 'hack', 'cheat'] // Add more as needed
    const lowerContent = content.toLowerCase()
    
    for (const word of profanityWords) {
      if (lowerContent.includes(word)) {
        return { valid: false, error: 'Message contains inappropriate content' }
      }
    }

    return { valid: true }
  }

  /**
   * Get chat history for a user (across all rooms)
   */
  static async getUserChatHistory(userId: string, limit: number = 50): Promise<ChatMessage[]> {
    try {
      const messages = await prisma.chatMessage.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit
      })

      return messages.map(message => ({
        id: message.id,
        roomId: message.roomId,
        userId: message.userId,
        username: message.username,
        content: message.content,
        messageType: message.messageType as 'user' | 'system' | 'admin',
        createdAt: message.createdAt
      }))
    } catch (error) {
      console.error('Error getting user chat history:', error)
      return []
    }
  }
}
