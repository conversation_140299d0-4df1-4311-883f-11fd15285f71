import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { prisma } from '../database/prisma.js'

export interface AuthUser {
  id: string
  email: string
  username: string
  profile?: {
    displayName: string | null
    level: number
    experience: number
    coins: number
    lastActiveAt: Date
  }
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterCredentials {
  email: string
  username: string
  password: string
  displayName?: string
}

export interface AuthResult {
  success: boolean
  user?: AuthUser
  token?: string
  error?: string
}

export class AuthService {
  private static readonly JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'
  private static readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'
  private static readonly SALT_ROUNDS = 12

  /**
   * Register a new user
   */
  static async register(credentials: RegisterCredentials): Promise<AuthResult> {
    try {
      const { email, username, password, displayName } = credentials

      // Validate input
      if (!email || !username || !password) {
        return { success: false, error: 'Email, username, and password are required' }
      }

      if (password.length < 6) {
        return { success: false, error: 'Password must be at least 6 characters long' }
      }

      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [{ email }, { username }]
        }
      })

      if (existingUser) {
        return { 
          success: false, 
          error: existingUser.email === email ? 'Email already registered' : 'Username already taken'
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS)

      // Create user with profile
      const user = await prisma.user.create({
        data: {
          email,
          username,
          password: hashedPassword,
          profile: {
            create: {
              displayName: displayName || username,
              level: 1,
              experience: 0,
              coins: 100
            }
          }
        },
        include: {
          profile: true
        }
      })

      // Generate JWT token
      const token = this.generateToken(user.id)

      // Store session
      await prisma.userSession.create({
        data: {
          userId: user.id,
          token,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        }
      })

      return {
        success: true,
        user: this.formatUser(user),
        token
      }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, error: 'Registration failed' }
    }
  }

  /**
   * Login user
   */
  static async login(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      const { email, password } = credentials

      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
        include: { profile: true }
      })

      if (!user) {
        return { success: false, error: 'Invalid email or password' }
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password)
      if (!isValidPassword) {
        return { success: false, error: 'Invalid email or password' }
      }

      // Generate new token
      const token = this.generateToken(user.id)

      // Store session
      await prisma.userSession.create({
        data: {
          userId: user.id,
          token,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        }
      })

      // Update last active
      if (user.profile) {
        await prisma.userProfile.update({
          where: { userId: user.id },
          data: { lastActiveAt: new Date() }
        })
      }

      return {
        success: true,
        user: this.formatUser(user),
        token
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Login failed' }
    }
  }

  /**
   * Verify JWT token and get user
   */
  static async verifyToken(token: string): Promise<AuthUser | null> {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as { userId: string }
      
      // Check if session exists and is valid
      const session = await prisma.userSession.findUnique({
        where: { token },
        include: {
          user: {
            include: { profile: true }
          }
        }
      })

      if (!session || session.expiresAt < new Date()) {
        return null
      }

      return this.formatUser(session.user)
    } catch (error) {
      return null
    }
  }

  /**
   * Logout user (invalidate session)
   */
  static async logout(token: string): Promise<boolean> {
    try {
      await prisma.userSession.delete({
        where: { token }
      })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Generate JWT token
   */
  private static generateToken(userId: string): string {
    return jwt.sign({ userId }, this.JWT_SECRET, { expiresIn: '7d' })
  }

  /**
   * Format user data for API response
   */
  private static formatUser(user: any): AuthUser {
    return {
      id: user.id,
      email: user.email,
      username: user.username,
      profile: user.profile ? {
        displayName: user.profile.displayName,
        level: user.profile.level,
        experience: user.profile.experience,
        coins: user.profile.coins,
        lastActiveAt: user.profile.lastActiveAt
      } : undefined
    }
  }
}
