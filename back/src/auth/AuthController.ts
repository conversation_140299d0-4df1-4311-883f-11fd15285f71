import { HttpRequest, HttpResponse } from 'uWebSockets.js'
import { AuthService, LoginCredentials, RegisterCredentials } from './AuthService.js'

export class AuthController {
  /**
   * Handle user registration
   */
  static async register(res: HttpResponse, req: HttpRequest) {
    try {
      // Read request body
      const body = await AuthController.readRequestBody(res)
      const credentials: RegisterCredentials = JSON.parse(body)

      // Validate required fields
      if (!credentials.email || !credentials.username || !credentials.password) {
        AuthController.sendResponse(res, 400, {
          success: false,
          error: 'Email, username, and password are required'
        })
        return
      }

      // Register user
      const result = await AuthService.register(credentials)

      if (result.success) {
        AuthController.sendResponse(res, 201, {
          success: true,
          user: result.user,
          token: result.token
        })
      } else {
        AuthController.sendResponse(res, 400, {
          success: false,
          error: result.error
        })
      }
    } catch (error) {
      console.error('Registration endpoint error:', error)
      AuthController.sendResponse(res, 500, {
        success: false,
        error: 'Internal server error'
      })
    }
  }

  /**
   * Handle user login
   */
  static async login(res: HttpResponse, req: HttpRequest) {
    try {
      // Read request body
      const body = await AuthController.readRequestBody(res)
      const credentials: LoginCredentials = JSON.parse(body)

      // Validate required fields
      if (!credentials.email || !credentials.password) {
        AuthController.sendResponse(res, 400, {
          success: false,
          error: 'Email and password are required'
        })
        return
      }

      // Login user
      const result = await AuthService.login(credentials)

      if (result.success) {
        AuthController.sendResponse(res, 200, {
          success: true,
          user: result.user,
          token: result.token
        })
      } else {
        AuthController.sendResponse(res, 401, {
          success: false,
          error: result.error
        })
      }
    } catch (error) {
      console.error('Login endpoint error:', error)
      AuthController.sendResponse(res, 500, {
        success: false,
        error: 'Internal server error'
      })
    }
  }

  /**
   * Handle user logout
   */
  static async logout(res: HttpResponse, req: HttpRequest) {
    try {
      const token = AuthController.extractToken(req)
      
      if (!token) {
        AuthController.sendResponse(res, 401, {
          success: false,
          error: 'No token provided'
        })
        return
      }

      const success = await AuthService.logout(token)
      
      AuthController.sendResponse(res, 200, {
        success,
        message: success ? 'Logged out successfully' : 'Logout failed'
      })
    } catch (error) {
      console.error('Logout endpoint error:', error)
      AuthController.sendResponse(res, 500, {
        success: false,
        error: 'Internal server error'
      })
    }
  }

  /**
   * Get current user profile
   */
  static async profile(res: HttpResponse, req: HttpRequest) {
    try {
      const token = AuthController.extractToken(req)
      
      if (!token) {
        AuthController.sendResponse(res, 401, {
          success: false,
          error: 'No token provided'
        })
        return
      }

      const user = await AuthService.verifyToken(token)
      
      if (!user) {
        AuthController.sendResponse(res, 401, {
          success: false,
          error: 'Invalid or expired token'
        })
        return
      }

      AuthController.sendResponse(res, 200, {
        success: true,
        user
      })
    } catch (error) {
      console.error('Profile endpoint error:', error)
      AuthController.sendResponse(res, 500, {
        success: false,
        error: 'Internal server error'
      })
    }
  }

  /**
   * Extract JWT token from Authorization header
   */
  private static extractToken(req: HttpRequest): string | null {
    const authHeader = req.getHeader('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7)
    }
    return null
  }

  /**
   * Read request body as string
   */
  private static readRequestBody(res: HttpResponse): Promise<string> {
    return new Promise((resolve, reject) => {
      let buffer = Buffer.alloc(0)
      
      res.onData((chunk, isLast) => {
        buffer = Buffer.concat([buffer, Buffer.from(chunk)])
        
        if (isLast) {
          resolve(buffer.toString())
        }
      })

      res.onAborted(() => {
        reject(new Error('Request aborted'))
      })
    })
  }

  /**
   * Send JSON response
   */
  private static sendResponse(res: HttpResponse, statusCode: number, data: any) {
    res.writeStatus(statusCode.toString())
    res.writeHeader('Content-Type', 'application/json')
    res.writeHeader('Access-Control-Allow-Origin', '*')
    res.writeHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    res.writeHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    res.end(JSON.stringify(data))
  }

  /**
   * Handle CORS preflight requests
   */
  static handleCors(res: HttpResponse, req: HttpRequest) {
    res.writeHeader('Access-Control-Allow-Origin', '*')
    res.writeHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    res.writeHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    res.writeStatus('200')
    res.end()
  }
}
