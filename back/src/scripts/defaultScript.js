function randomHexColor() {
  const hex = Math.floor(Math.random() * 16777215).toString(16)
  return '#' + '0'.repeat(6 - hex.length) + hex
}

// Nintendo-style bright colors palette
function getNintendoColor() {
  const nintendoColors = [
    '#FF6B6B', // Bright Red (<PERSON>)
    '#4ECDC4', // Turquoise (<PERSON>l)
    '#45B7D1', // Sky Blue
    '#96CEB4', // Mint Green
    '#FFEAA7', // Sunny Yellow
    '#DDA0DD', // Plum Purple
    '#FFB347', // Peach Orange
    '#98D8C8', // Seafoam Green
    '#F7DC6F', // Golden Yellow
    '#BB8FCE', // Lavender Purple
    '#85C1E9', // Light Blue
    '#F8C471'  // Light Orange
  ]
  return nintendoColors[Math.floor(Math.random() * nintendoColors.length)]
}

// Load the game world - Enhanced Nintendo-style island environment
// Using local assets for better performance and reliability
new MapWorld('http://localhost:4001/lexialia-egg.glb') // Using available local asset as base world

// === Basic Entity Creation Examples ===

// Create a basic cube
const basicCubeParams = {
  position: { x: 0, y: 5, z: -50 },
  size: { width: 3, height: 3, depth: 3 },
}
new Cube(basicCubeParams)

// Create physics-enabled sphere with a white color
const basicSphereParams = {
  position: { x: 5, y: 10, z: -10 },
  radius: 6,
  color: '#ffffff',
}
new Sphere(basicSphereParams)

// === Interactive Trigger Zone Example ===
// Creates an invisible trigger zone that detects when players enter/exit
const triggerCube = new TriggerCube(
  -100,
  -5,
  -200, // position
  8,
  8,
  8, // size
  (entity) => {
    // onEnter callback
    if (entity.getComponent(PlayerComponent)) {
      console.log('Invisible trigger zone: Player entered the zone!')
      // You could add game logic here, like:
      // - Giving points
      // - Triggering events
      // - Spawning enemies
      entity
        .getComponent(DynamicRigidBodyComponent)
        .body.applyImpulse(new Rapier.Vector3(0, 9000, 0), true)
    }
  },
  (entity) => {
    // onExit callback
    if (entity.getComponent(PlayerComponent)) {
      console.log('Invisible trigger zone: Player left the zone!')
    }
  },
  true // Set to true to see the trigger zone (useful for debugging)
)
triggerCube.entity.addNetworkComponent(
  new TextComponent(triggerCube.entity.id, 'Trampoline', 0, 2, 0, 30)
)

// === Interactive Object Example ===
// Create a cube that reacts to player collision
for (let i = 0; i < 2; i++) {
  const interactiveCubeParams = {
    position: { x: 0, y: 5, z: -100 },
    size: { width: 2, height: 2, depth: 2 },
    physicsProperties: {
      enableCcd: true,
    },
  }
  const interactiveCube = new Cube(interactiveCubeParams)
  interactiveCube.entity.addComponent(
    new OnCollisionEnterEvent(interactiveCube.entity.id, (collidedWithEntity) => {
      // Only react to players
      if (collidedWithEntity.getComponent(PlayerComponent)) {
        // Change color of the cube on collision
        EventSystem.addEvent(new ColorEvent(interactiveCube.entity.id, randomHexColor()))

        // Apply upward force
        const rigidBody = interactiveCube.entity.getComponent(DynamicRigidBodyComponent)
        if (rigidBody) {
          rigidBody.body.applyImpulse(new Rapier.Vector3(0, 5000, 0), true)
        }
      }
    })
  )
}

// Nintendo-style Angel Character using local asset
const angelCharacter = new Mesh({
  position: {
    x: -100,
    y: 10,
    z: 100,
  },
  meshUrl: 'http://localhost:4001/karola-angel-idle.glb', // Using local Nintendo-style character
  physicsProperties: {
    mass: 1,
    angularDamping: 0.5,
    enableCcd: true,
  },
  colliderProperties: {
    restitution: 0.7,
  },
})
angelCharacter.entity.addNetworkComponent(new ColorComponent(angelCharacter.entity.id, '#FFD700')) // Golden Nintendo-style color
angelCharacter.entity.addComponent(new ZombieComponent(angelCharacter.entity.id))
angelCharacter.entity.addNetworkComponent(new TextComponent(angelCharacter.entity.id, '👼 Karola Angel', 0, 2, 0))

// Add magical Merlin character for medieval theme
const merlinCharacter = new Mesh({
  position: {
    x: 50,
    y: 10,
    z: -50,
  },
  meshUrl: 'http://localhost:4001/merlin.glb', // Using local Merlin asset
  physicsProperties: {
    mass: 1,
    angularDamping: 0.5,
    enableCcd: true,
  },
  colliderProperties: {
    restitution: 0.7,
  },
})
merlinCharacter.entity.addNetworkComponent(new ColorComponent(merlinCharacter.entity.id, '#8A2BE2')) // Purple magical color
merlinCharacter.entity.addComponent(new ZombieComponent(merlinCharacter.entity.id))
merlinCharacter.entity.addNetworkComponent(new TextComponent(merlinCharacter.entity.id, '�‍♂️ Merlin the Wise', 0, 2, 0))

// === Create Multiple Objects Example ===
// Creates a line of cubes with alternating colors
// const colors = ['#ff0000', '#00ff00', '#0000ff']
// for (let i = 0; i < 2; i++) {
//   const cubeParams = {
//     position: { x: i * 3, y: 5, z: -40 },
//     size: { width: 1, height: 1, depth: 1 },
//     color: colors[i % colors.length],
//   }
//   const cube = new Cube(cubeParams)
//   cube.entity.addComponent(new RandomizeComponent(cube.entity.id))
//   const sphereParams = {
//     position: { x: i * 3, y: 5, z: -40 },
//     radius: 1,
//     color: colors[i % colors.length],
//   }
//   const sphere = new Sphere(sphereParams)
//   sphere.entity.addComponent(new RandomizeComponent(sphere.entity.id))
// }

const cube = new Cube({
  position: {
    x: 100,
    y: 10,
    z: 100,
  },
  physicsProperties: {
    mass: 1,
    angularDamping: 0.5,
  },
})
const proximityPromptComponent = new ProximityPromptComponent(cube.entity.id, {
  text: 'Press E to change color',
  onInteract: (interactingEntity) => {
    cube.entity
      .getComponent(DynamicRigidBodyComponent)
      .body.applyImpulse(new Rapier.Vector3(0, 5, 0), true)

    const colorComponent = cube.entity.getComponent(ColorComponent)
    if (colorComponent) {
      // randomize color
      colorComponent.color = '#' + Math.floor(Math.random() * 16777215).toString(16)
      colorComponent.updated = true
    }
  },
  maxInteractDistance: 10,
  interactionCooldown: 200,
  holdDuration: 0,
})
cube.entity.addNetworkComponent(proximityPromptComponent)

for (let i = -3; i < 6; i++) {
  if (i === 0) continue
  const x = 5 * -i
  const y = 5
  const z = 20 * i
  const car = new Car({
    position: { x, y, z },
  })
  car.entity.addComponent(new SpawnPositionComponent(car.entity.id, x, y, z))
}

// Create a row of cars with varying wheel sizes
for (let i = 1; i < 10; i++) {
  const x = -140 + 5 * -i
  const y = 5
  const z = 20 * i

  // Configure wheels with varying sizes
  let wheelConfig = {}
  if (i < 5) {
    // Small front wheels for first set of cars
    wheelConfig = {
      frontLeft: Math.max(1, i / 2.5),
      frontRight: Math.max(1, i / 2.5),
      backLeft: 1.4,
      backRight: 1.4,
    }
  } else {
    // Small back wheels for second set of cars
    wheelConfig = {
      frontLeft: 1.4,
      frontRight: 1.4,
      backLeft: Math.max(1, (10 - i) / 2.5),
      backRight: Math.max(1, (10 - i) / 2.5),
    }
  }

  const car = new Car({
    position: { x, y, z },
    name: 'Nintendo Kart',
    meshUrl: 'http://localhost:4001/Car.glb', // Using local car asset
    wheelRadius: wheelConfig,
    color: getNintendoColor(), // Using Nintendo-style colors
  })
  car.entity.addComponent(new SpawnPositionComponent(car.entity.id, x, y, z))
}

const magicCar = new Car({
  position: { x: 0, y: 5, z: -500 },
  name: 'Magic Kart',
  meshUrl: 'http://localhost:4001/Car.glb', // Using local car asset
  color: '#FFD700', // Golden magic car
})
magicCar.entity.addComponent(new SpawnPositionComponent(magicCar.entity.id, 250, 20, -500))

const speedKart = new Car({
  position: { x: 0, y: 5, z: -500 },
  name: 'Speed Kart',
  meshUrl: 'http://localhost:4001/EzCar.glb', // Using local EzCar asset
  color: '#FF6B6B', // Bright red like Mario
})
speedKart.entity.addComponent(new SpawnPositionComponent(speedKart.entity.id, 150, 20, -500))

// Flying Car (different gravity scale, slow motion)
new Car({
  position: { x: 0, y: 20, z: -500 },
  physicsProperties: {
    gravityScale: 0.05,
    enableCcd: true,
  },
  name: 'Flying Car',
  color: '#78b5ff',
})

// Football test
function spawnFootballBall() {
  const ballSpawnPosition = { x: 0, y: 20, z: -10 }

  const sphereParams = {
    radius: 1.4,
    position: {
      x: ballSpawnPosition.x,
      y: ballSpawnPosition.y,
      z: ballSpawnPosition.z,
    },
    meshUrl: 'https://notbloxo.fra1.cdn.digitaloceanspaces.com/Notblox-Assets/base/Ball.glb',
    physicsProperties: {
      mass: 1,
      // Enable continuous collision detection to prevent the ball from going through the walls
      enableCcd: true,
      angularDamping: 0.5,
      linearDamping: 0.5,
    },
    colliderProperties: {
      friction: 0.4,
      restitution: 0.8,
    },
  }

  let ball
  // Initialize the ball using SphereParams
  ball = new Sphere(sphereParams)
  ball.entity.addComponent(
    new SpawnPositionComponent(
      ball.entity.id,
      ballSpawnPosition.x,
      ballSpawnPosition.y,
      ballSpawnPosition.z
    )
  )

  const proximityPromptComponent = new ProximityPromptComponent(ball.entity.id, {
    text: 'Kick',
    onInteract: (playerEntity) => {
      const ballRigidbody = ball.entity.getComponent(DynamicRigidBodyComponent)
      const playerRotationComponent = playerEntity.getComponent(RotationComponent)

      if (ballRigidbody && playerRotationComponent && playerEntity.getComponent(InputComponent)) {
        // Convert rotation to direction vector
        const direction = playerRotationComponent.getForwardDirection()
        // Calculate player looking direction
        // sendChatMessage('⚽', `Player shot the ball !`)
        const playerLookingDirectionVector = new Rapier.Vector3(
          direction.x * 1500,
          0,
          direction.z * 1500
        )

        ballRigidbody.body.applyImpulse(playerLookingDirectionVector, true)
      }
    },
    maxInteractDistance: 10,
    interactionCooldown: 2000,
    holdDuration: 0,
  })
  ball.entity.addNetworkComponent(proximityPromptComponent)
}

spawnFootballBall()

// === Nintendo-Style Magical Elements ===

// Create colorful magical orbs floating around
for (let i = 0; i < 8; i++) {
  const angle = (i / 8) * Math.PI * 2
  const radius = 80
  const x = Math.cos(angle) * radius
  const z = Math.sin(angle) * radius

  const magicalOrb = new Sphere({
    position: { x: x, y: 15 + Math.sin(i) * 5, z: z },
    radius: 2,
    color: getNintendoColor(),
    physicsProperties: {
      mass: 0.1,
      gravityScale: -0.1, // Floating effect
    },
  })
  magicalOrb.entity.addNetworkComponent(new TextComponent(magicalOrb.entity.id, '✨', 0, 3, 0))
}

// Create magical books scattered around (using magic-book.glb)
const bookPositions = [
  { x: 30, y: 5, z: 30 },
  { x: -30, y: 5, z: 30 },
  { x: 30, y: 5, z: -30 },
  { x: -30, y: 5, z: -30 },
  { x: 0, y: 5, z: 60 },
]

bookPositions.forEach((pos, index) => {
  const magicBook = new Mesh({
    position: pos,
    meshUrl: 'http://localhost:4001/magic-book.glb',
    physicsProperties: {
      mass: 1,
      angularDamping: 0.8,
    },
    colliderProperties: {
      restitution: 0.3,
    },
  })
  magicBook.entity.addNetworkComponent(new ColorComponent(magicBook.entity.id, getNintendoColor()))
  magicBook.entity.addNetworkComponent(new TextComponent(magicBook.entity.id, `📚 Spell Book ${index + 1}`, 0, 2, 0))

  // Add interaction to books
  const bookPrompt = new ProximityPromptComponent(magicBook.entity.id, {
    text: 'Press E to cast magic spell!',
    onInteract: (playerEntity) => {
      // Create magical effect
      const rigidBody = magicBook.entity.getComponent(DynamicRigidBodyComponent)
      if (rigidBody) {
        rigidBody.body.applyImpulse(new Rapier.Vector3(0, 8000, 0), true)
      }
      // Change color for magical effect
      const colorComponent = magicBook.entity.getComponent(ColorComponent)
      if (colorComponent) {
        colorComponent.color = getNintendoColor()
        colorComponent.updated = true
      }
    },
    maxInteractDistance: 8,
    interactionCooldown: 1000,
    holdDuration: 0,
  })
  magicBook.entity.addNetworkComponent(bookPrompt)
})
