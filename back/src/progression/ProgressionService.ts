import { prisma } from '../database/prisma.js'

export interface ProgressionUpdate {
  experienceGained: number
  coinsGained?: number
  reason?: string
}

export interface LevelUpResult {
  leveledUp: boolean
  newLevel: number
  experienceGained: number
  coinsGained: number
  totalExperience: number
  totalCoins: number
}

export class ProgressionService {
  /**
   * Calculate XP required for a specific level
   */
  static getXPRequiredForLevel(level: number): number {
    // Simple formula: level 1 = 0 XP, level 2 = 100 XP, level 3 = 300 XP, etc.
    // Formula: (level - 1) * 100 + (level - 1) * (level - 2) * 25
    if (level <= 1) return 0
    return (level - 1) * 100 + (level - 1) * (level - 2) * 25
  }

  /**
   * Calculate level from total XP
   */
  static getLevelFromXP(totalXP: number): number {
    let level = 1
    while (this.getXPRequiredForLevel(level + 1) <= totalXP) {
      level++
    }
    return level
  }

  /**
   * Add experience and handle level ups
   */
  static async addExperience(userId: string, experienceGained: number, coinsGained: number = 0, reason?: string): Promise<LevelUpResult> {
    try {
      const profile = await prisma.userProfile.findUnique({
        where: { userId }
      })

      if (!profile) {
        throw new Error('User profile not found')
      }

      const currentLevel = profile.level
      const currentXP = profile.experience
      const currentCoins = profile.coins

      const newTotalXP = currentXP + experienceGained
      const newLevel = this.getLevelFromXP(newTotalXP)
      const leveledUp = newLevel > currentLevel

      // Calculate bonus coins for leveling up
      let totalCoinsGained = coinsGained
      if (leveledUp) {
        const levelsGained = newLevel - currentLevel
        totalCoinsGained += levelsGained * 50 // 50 coins per level
      }

      const newTotalCoins = currentCoins + totalCoinsGained

      // Update profile
      await prisma.userProfile.update({
        where: { userId },
        data: {
          level: newLevel,
          experience: newTotalXP,
          coins: newTotalCoins,
          lastActiveAt: new Date()
        }
      })

      // Log progression event (optional, for analytics)
      if (reason) {
        console.log(`User ${userId} gained ${experienceGained} XP for: ${reason}`)
      }

      return {
        leveledUp,
        newLevel,
        experienceGained,
        coinsGained: totalCoinsGained,
        totalExperience: newTotalXP,
        totalCoins: newTotalCoins
      }
    } catch (error) {
      console.error('Error adding experience:', error)
      throw error
    }
  }

  /**
   * Add coins only
   */
  static async addCoins(userId: string, coinsGained: number, reason?: string): Promise<number> {
    try {
      const profile = await prisma.userProfile.update({
        where: { userId },
        data: {
          coins: {
            increment: coinsGained
          },
          lastActiveAt: new Date()
        }
      })

      if (reason) {
        console.log(`User ${userId} gained ${coinsGained} coins for: ${reason}`)
      }

      return profile.coins
    } catch (error) {
      console.error('Error adding coins:', error)
      throw error
    }
  }

  /**
   * Spend coins
   */
  static async spendCoins(userId: string, coinsToSpend: number, reason?: string): Promise<{ success: boolean, remainingCoins: number }> {
    try {
      const profile = await prisma.userProfile.findUnique({
        where: { userId }
      })

      if (!profile) {
        return { success: false, remainingCoins: 0 }
      }

      if (profile.coins < coinsToSpend) {
        return { success: false, remainingCoins: profile.coins }
      }

      const updatedProfile = await prisma.userProfile.update({
        where: { userId },
        data: {
          coins: profile.coins - coinsToSpend,
          lastActiveAt: new Date()
        }
      })

      if (reason) {
        console.log(`User ${userId} spent ${coinsToSpend} coins for: ${reason}`)
      }

      return { success: true, remainingCoins: updatedProfile.coins }
    } catch (error) {
      console.error('Error spending coins:', error)
      return { success: false, remainingCoins: 0 }
    }
  }

  /**
   * Get progression stats
   */
  static async getProgressionStats(userId: string) {
    try {
      const profile = await prisma.userProfile.findUnique({
        where: { userId }
      })

      if (!profile) return null

      const currentLevel = profile.level
      const currentXP = profile.experience
      const xpForCurrentLevel = this.getXPRequiredForLevel(currentLevel)
      const xpForNextLevel = this.getXPRequiredForLevel(currentLevel + 1)
      const xpInCurrentLevel = currentXP - xpForCurrentLevel
      const xpNeededForNextLevel = xpForNextLevel - currentXP

      return {
        level: currentLevel,
        totalExperience: currentXP,
        coins: profile.coins,
        xpProgress: {
          current: xpInCurrentLevel,
          needed: xpForNextLevel - xpForCurrentLevel,
          percentage: ((xpInCurrentLevel) / (xpForNextLevel - xpForCurrentLevel)) * 100
        },
        nextLevel: {
          level: currentLevel + 1,
          xpNeeded: xpNeededForNextLevel
        }
      }
    } catch (error) {
      console.error('Error getting progression stats:', error)
      return null
    }
  }

  /**
   * Award experience for common game actions
   */
  static async awardActionXP(userId: string, action: string): Promise<LevelUpResult | null> {
    const xpRewards: { [key: string]: { xp: number, coins: number } } = {
      'join_game': { xp: 10, coins: 5 },
      'play_minute': { xp: 5, coins: 1 },
      'interact_object': { xp: 2, coins: 0 },
      'send_message': { xp: 1, coins: 0 },
      'complete_objective': { xp: 50, coins: 25 },
      'win_game': { xp: 100, coins: 50 },
      'daily_login': { xp: 25, coins: 10 }
    }

    const reward = xpRewards[action]
    if (!reward) {
      console.warn(`Unknown action for XP reward: ${action}`)
      return null
    }

    try {
      return await this.addExperience(userId, reward.xp, reward.coins, action)
    } catch (error) {
      console.error(`Error awarding XP for action ${action}:`, error)
      return null
    }
  }

  /**
   * Get leaderboard
   */
  static async getLeaderboard(limit: number = 10) {
    try {
      const profiles = await prisma.userProfile.findMany({
        include: {
          user: {
            select: {
              username: true
            }
          }
        },
        orderBy: [
          { level: 'desc' },
          { experience: 'desc' }
        ],
        take: limit
      })

      return profiles.map((profile, index) => ({
        rank: index + 1,
        username: profile.user.username,
        displayName: profile.displayName,
        level: profile.level,
        experience: profile.experience,
        coins: profile.coins
      }))
    } catch (error) {
      console.error('Error getting leaderboard:', error)
      return []
    }
  }

  /**
   * Update play time
   */
  static async updatePlayTime(userId: string, secondsPlayed: number): Promise<boolean> {
    try {
      await prisma.userProfile.update({
        where: { userId },
        data: {
          totalPlayTime: {
            increment: secondsPlayed
          },
          lastActiveAt: new Date()
        }
      })

      // Award XP for play time (every minute)
      const minutesPlayed = Math.floor(secondsPlayed / 60)
      if (minutesPlayed > 0) {
        await this.awardActionXP(userId, 'play_minute')
      }

      return true
    } catch (error) {
      console.error('Error updating play time:', error)
      return false
    }
  }

  /**
   * Increment games played counter
   */
  static async incrementGamesPlayed(userId: string): Promise<boolean> {
    try {
      await prisma.userProfile.update({
        where: { userId },
        data: {
          gamesPlayed: {
            increment: 1
          },
          lastActiveAt: new Date()
        }
      })

      return true
    } catch (error) {
      console.error('Error incrementing games played:', error)
      return false
    }
  }
}
