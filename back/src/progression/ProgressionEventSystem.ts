import { Entity } from '../../../shared/entity/Entity.js'
import { PlayerComponent } from '../../../shared/component/PlayerComponent.js'
import { EntityManager } from '../../../shared/system/EntityManager.js'
import { EventSystem } from '../../../shared/system/EventSystem.js'
import { MessageEvent } from '../ecs/component/events/MessageEvent.js'
import { ProgressionService, LevelUpResult } from './ProgressionService.js'
import { Player } from '../ecs/entity/Player.js'

export interface ProgressionEvent {
  playerId: number
  action: string
  xpGained: number
  coinsGained: number
  levelUp?: boolean
  newLevel?: number
}

export class ProgressionEventSystem {
  private progressionEvents: ProgressionEvent[] = []
  private lastPlayTimeUpdate: Map<number, number> = new Map()
  private readonly PLAY_TIME_UPDATE_INTERVAL = 60000 // Update play time every minute

  /**
   * Add a progression event
   */
  addProgressionEvent(event: ProgressionEvent) {
    this.progressionEvents.push(event)
  }

  /**
   * Update progression system
   */
  update(entities: Entity[], players: Player[]) {
    // Process progression events
    this.processProgressionEvents()
    
    // Update play time for active players
    this.updatePlayTime(players)
    
    // Check for daily login bonuses
    this.checkDailyLoginBonuses(players)
  }

  /**
   * Process pending progression events
   */
  private processProgressionEvents() {
    for (const event of this.progressionEvents) {
      this.handleProgressionEvent(event)
    }
    this.progressionEvents = []
  }

  /**
   * Handle a single progression event
   */
  private handleProgressionEvent(event: ProgressionEvent) {
    // Send progression notification
    const message = event.levelUp 
      ? `🎉 Level Up! You reached level ${event.newLevel}! (+${event.xpGained} XP, +${event.coinsGained} coins)`
      : `+${event.xpGained} XP, +${event.coinsGained} coins (${event.action})`

    EventSystem.addEvent(
      new MessageEvent(
        event.playerId,
        '🏆 [PROGRESSION]',
        message
      )
    )

    console.log(`Progression event for player ${event.playerId}: ${message}`)
  }

  /**
   * Award XP to a player and handle level ups
   */
  async awardXP(player: Player, action: string): Promise<LevelUpResult | null> {
    if (!player.user) return null

    try {
      const result = await ProgressionService.awardActionXP(player.user.id, action)
      
      if (result) {
        this.addProgressionEvent({
          playerId: player.entity.id,
          action,
          xpGained: result.experienceGained,
          coinsGained: result.coinsGained,
          levelUp: result.leveledUp,
          newLevel: result.leveledUp ? result.newLevel : undefined
        })

        // Update player's cached profile data if available
        if (player.user.profile) {
          player.user.profile.level = result.newLevel
          player.user.profile.experience = result.totalExperience
          player.user.profile.coins = result.totalCoins
        }
      }

      return result
    } catch (error) {
      console.error('Error awarding XP:', error)
      return null
    }
  }

  /**
   * Update play time for active players
   */
  private updatePlayTime(players: Player[]) {
    const now = Date.now()

    for (const player of players) {
      if (!player.user) continue

      const lastUpdate = this.lastPlayTimeUpdate.get(player.entity.id) || player.joinTime
      
      if (now - lastUpdate >= this.PLAY_TIME_UPDATE_INTERVAL) {
        const secondsPlayed = Math.floor((now - lastUpdate) / 1000)
        
        // Update play time in database
        ProgressionService.updatePlayTime(player.user.id, secondsPlayed)
          .then(() => {
            this.lastPlayTimeUpdate.set(player.entity.id, now)
          })
          .catch(error => {
            console.error('Error updating play time:', error)
          })
      }
    }
  }

  /**
   * Check for daily login bonuses
   */
  private async checkDailyLoginBonuses(players: Player[]) {
    for (const player of players) {
      if (!player.user || !player.user.profile) continue

      const lastActive = player.user.profile.lastActiveAt
      const now = new Date()
      
      // Check if it's a new day since last login
      if (lastActive) {
        const lastActiveDate = new Date(lastActive)
        const isNewDay = lastActiveDate.toDateString() !== now.toDateString()
        
        if (isNewDay) {
          // Award daily login bonus
          await this.awardXP(player, 'daily_login')
        }
      }
    }
  }

  /**
   * Award XP for specific game actions
   */
  async awardActionXP(player: Player, action: string, customXP?: number, customCoins?: number): Promise<LevelUpResult | null> {
    if (!player.user) return null

    try {
      let result: LevelUpResult | null = null

      if (customXP !== undefined || customCoins !== undefined) {
        // Custom XP/coins amount
        result = await ProgressionService.addExperience(
          player.user.id, 
          customXP || 0, 
          customCoins || 0, 
          action
        )
      } else {
        // Use predefined action rewards
        result = await ProgressionService.awardActionXP(player.user.id, action)
      }

      if (result) {
        this.addProgressionEvent({
          playerId: player.entity.id,
          action,
          xpGained: result.experienceGained,
          coinsGained: result.coinsGained,
          levelUp: result.leveledUp,
          newLevel: result.leveledUp ? result.newLevel : undefined
        })

        // Update player's cached profile data
        if (player.user.profile) {
          player.user.profile.level = result.newLevel
          player.user.profile.experience = result.totalExperience
          player.user.profile.coins = result.totalCoins
        }
      }

      return result
    } catch (error) {
      console.error('Error awarding action XP:', error)
      return null
    }
  }

  /**
   * Get progression stats for a player
   */
  async getProgressionStats(player: Player) {
    if (!player.user) return null

    try {
      return await ProgressionService.getProgressionStats(player.user.id)
    } catch (error) {
      console.error('Error getting progression stats:', error)
      return null
    }
  }

  /**
   * Get leaderboard
   */
  async getLeaderboard(limit: number = 10) {
    try {
      return await ProgressionService.getLeaderboard(limit)
    } catch (error) {
      console.error('Error getting leaderboard:', error)
      return []
    }
  }

  /**
   * Clean up player data when they disconnect
   */
  onPlayerDisconnect(playerId: number) {
    this.lastPlayTimeUpdate.delete(playerId)
  }

  /**
   * Get progression statistics
   */
  getStats() {
    return {
      pendingEvents: this.progressionEvents.length,
      activePlayTimers: this.lastPlayTimeUpdate.size
    }
  }
}
