import { prisma } from '../database/prisma.js'
import { RoomManager } from '../rooms/RoomManager.js'

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  uptime: number
  version: string
  services: {
    database: {
      status: 'connected' | 'disconnected'
      responseTime?: number
    }
    gameServer: {
      status: 'running' | 'stopped'
      playerCount: number
      tickRate: number
    }
    rooms: {
      status: 'available' | 'unavailable'
      roomCount: number
    }
  }
  memory: {
    used: number
    total: number
    percentage: number
  }
}

export class HealthController {
  private static startTime = Date.now()
  private static websocketSystem: any = null

  /**
   * Set WebSocket system reference for health checks
   */
  static setWebSocketSystem(wsSystem: any) {
    this.websocketSystem = wsSystem
  }

  /**
   * Get comprehensive health status
   */
  static async getHealthStatus(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString()
    const uptime = Date.now() - this.startTime

    // Check database connection
    const dbStatus = await this.checkDatabaseHealth()
    
    // Check game server status
    const gameServerStatus = this.checkGameServerHealth()
    
    // Check rooms status
    const roomsStatus = await this.checkRoomsHealth()
    
    // Get memory usage
    const memoryStatus = this.getMemoryStatus()

    // Determine overall status
    const overallStatus = this.determineOverallStatus(dbStatus, gameServerStatus, roomsStatus)

    return {
      status: overallStatus,
      timestamp,
      uptime,
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: dbStatus,
        gameServer: gameServerStatus,
        rooms: roomsStatus
      },
      memory: memoryStatus
    }
  }

  /**
   * Check database health
   */
  private static async checkDatabaseHealth() {
    try {
      const startTime = Date.now()
      await prisma.$queryRaw`SELECT 1`
      const responseTime = Date.now() - startTime

      return {
        status: 'connected' as const,
        responseTime
      }
    } catch (error) {
      console.error('Database health check failed:', error)
      return {
        status: 'disconnected' as const
      }
    }
  }

  /**
   * Check game server health
   */
  private static checkGameServerHealth() {
    const playerCount = this.websocketSystem ? this.websocketSystem.getPlayerCount() : 0

    return {
      status: 'running' as const,
      playerCount,
      tickRate: parseInt(process.env.GAME_TICKRATE || '20')
    }
  }

  /**
   * Check rooms health
   */
  private static async checkRoomsHealth() {
    try {
      const roomManager = RoomManager.getInstance()
      const rooms = await roomManager.getAllRooms()

      return {
        status: 'available' as const,
        roomCount: rooms.length
      }
    } catch (error) {
      console.error('Rooms health check failed:', error)
      return {
        status: 'unavailable' as const,
        roomCount: 0
      }
    }
  }

  /**
   * Get memory usage statistics
   */
  private static getMemoryStatus() {
    const memUsage = process.memoryUsage()
    const totalMemory = memUsage.heapTotal
    const usedMemory = memUsage.heapUsed
    const percentage = (usedMemory / totalMemory) * 100

    return {
      used: Math.round(usedMemory / 1024 / 1024), // MB
      total: Math.round(totalMemory / 1024 / 1024), // MB
      percentage: Math.round(percentage * 100) / 100
    }
  }

  /**
   * Determine overall health status
   */
  private static determineOverallStatus(
    dbStatus: any,
    gameServerStatus: any,
    roomsStatus: any
  ): 'healthy' | 'degraded' | 'unhealthy' {
    if (dbStatus.status === 'disconnected') {
      return 'unhealthy'
    }

    if (gameServerStatus.status === 'stopped' || roomsStatus.status === 'unavailable') {
      return 'degraded'
    }

    // Check response times
    if (dbStatus.responseTime && dbStatus.responseTime > 1000) {
      return 'degraded'
    }

    return 'healthy'
  }

  /**
   * Simple health check endpoint
   */
  static async getSimpleHealth(): Promise<{ status: string, timestamp: string }> {
    try {
      await prisma.$queryRaw`SELECT 1`
      return {
        status: 'ok',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get detailed metrics for monitoring
   */
  static async getMetrics() {
    try {
      // Database metrics
      const userCount = await prisma.user.count()
      const activeCharacters = await prisma.character.count({
        where: { isOnline: true }
      })
      const totalMessages = await prisma.chatMessage.count()
      const totalRooms = await prisma.room.count()

      // Memory metrics
      const memUsage = process.memoryUsage()

      return {
        users: {
          total: userCount,
          active: activeCharacters
        },
        chat: {
          totalMessages
        },
        rooms: {
          total: totalRooms
        },
        memory: {
          heapUsed: memUsage.heapUsed,
          heapTotal: memUsage.heapTotal,
          external: memUsage.external,
          rss: memUsage.rss
        },
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error getting metrics:', error)
      return {
        error: 'Failed to get metrics',
        timestamp: new Date().toISOString()
      }
    }
  }
}
