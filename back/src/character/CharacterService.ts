import { prisma } from '../database/prisma.js'

export interface CharacterData {
  id: string
  userId: string
  name: string
  positionX: number
  positionY: number
  positionZ: number
  rotationY: number
  color: string
  size: number
  currentRoom: string
  isOnline: boolean
  lastSeen: Date
}

export interface CharacterUpdate {
  positionX?: number
  positionY?: number
  positionZ?: number
  rotationY?: number
  color?: string
  size?: number
  currentRoom?: string
  isOnline?: boolean
}

export class CharacterService {
  /**
   * Get or create character for user
   */
  static async getOr<PERSON><PERSON><PERSON><PERSON><PERSON>(userId: string, username: string): Promise<CharacterData | null> {
    try {
      // Try to find existing character
      let character = await prisma.character.findFirst({
        where: { userId }
      })

      // Create new character if none exists
      if (!character) {
        character = await prisma.character.create({
          data: {
            userId,
            name: username,
            positionX: Math.random() * 10 - 5, // Random spawn position
            positionY: 5,
            positionZ: Math.random() * 10 - 5,
            rotationY: 0,
            color: '#3b82f6', // Default blue color
            size: 1.5,
            currentRoom: 'world-1',
            isOnline: true,
            lastSeen: new Date()
          }
        })
      } else {
        // Update online status and last seen
        character = await prisma.character.update({
          where: { id: character.id },
          data: {
            isOnline: true,
            lastSeen: new Date()
          }
        })
      }

      return {
        id: character.id,
        userId: character.userId,
        name: character.name,
        positionX: character.positionX,
        positionY: character.positionY,
        positionZ: character.positionZ,
        rotationY: character.rotationY,
        color: character.color,
        size: character.size,
        currentRoom: character.currentRoom,
        isOnline: character.isOnline,
        lastSeen: character.lastSeen
      }
    } catch (error) {
      console.error('Error getting or creating character:', error)
      return null
    }
  }

  /**
   * Update character data
   */
  static async updateCharacter(characterId: string, updates: CharacterUpdate): Promise<boolean> {
    try {
      await prisma.character.update({
        where: { id: characterId },
        data: {
          ...updates,
          lastSeen: new Date()
        }
      })
      return true
    } catch (error) {
      console.error('Error updating character:', error)
      return false
    }
  }

  /**
   * Set character offline
   */
  static async setCharacterOffline(characterId: string): Promise<boolean> {
    try {
      await prisma.character.update({
        where: { id: characterId },
        data: {
          isOnline: false,
          lastSeen: new Date()
        }
      })
      return true
    } catch (error) {
      console.error('Error setting character offline:', error)
      return false
    }
  }

  /**
   * Get character by ID
   */
  static async getCharacterById(characterId: string): Promise<CharacterData | null> {
    try {
      const character = await prisma.character.findUnique({
        where: { id: characterId }
      })

      if (!character) return null

      return {
        id: character.id,
        userId: character.userId,
        name: character.name,
        positionX: character.positionX,
        positionY: character.positionY,
        positionZ: character.positionZ,
        rotationY: character.rotationY,
        color: character.color,
        size: character.size,
        currentRoom: character.currentRoom,
        isOnline: character.isOnline,
        lastSeen: character.lastSeen
      }
    } catch (error) {
      console.error('Error getting character by ID:', error)
      return null
    }
  }

  /**
   * Get characters in room
   */
  static async getCharactersInRoom(roomName: string): Promise<CharacterData[]> {
    try {
      const characters = await prisma.character.findMany({
        where: {
          currentRoom: roomName,
          isOnline: true
        }
      })

      return characters.map(character => ({
        id: character.id,
        userId: character.userId,
        name: character.name,
        positionX: character.positionX,
        positionY: character.positionY,
        positionZ: character.positionZ,
        rotationY: character.rotationY,
        color: character.color,
        size: character.size,
        currentRoom: character.currentRoom,
        isOnline: character.isOnline,
        lastSeen: character.lastSeen
      }))
    } catch (error) {
      console.error('Error getting characters in room:', error)
      return []
    }
  }

  /**
   * Change character room
   */
  static async changeCharacterRoom(characterId: string, newRoom: string, spawnPosition?: { x: number, y: number, z: number }): Promise<boolean> {
    try {
      const updateData: any = {
        currentRoom: newRoom,
        lastSeen: new Date()
      }

      if (spawnPosition) {
        updateData.positionX = spawnPosition.x
        updateData.positionY = spawnPosition.y
        updateData.positionZ = spawnPosition.z
      }

      await prisma.character.update({
        where: { id: characterId },
        data: updateData
      })

      return true
    } catch (error) {
      console.error('Error changing character room:', error)
      return false
    }
  }

  /**
   * Get character statistics
   */
  static async getCharacterStats(characterId: string) {
    try {
      const character = await prisma.character.findUnique({
        where: { id: characterId },
        include: {
          user: {
            include: {
              profile: true
            }
          }
        }
      })

      if (!character) return null

      return {
        character: {
          id: character.id,
          name: character.name,
          currentRoom: character.currentRoom,
          isOnline: character.isOnline,
          lastSeen: character.lastSeen
        },
        profile: character.user.profile ? {
          level: character.user.profile.level,
          experience: character.user.profile.experience,
          coins: character.user.profile.coins,
          totalPlayTime: character.user.profile.totalPlayTime,
          gamesPlayed: character.user.profile.gamesPlayed
        } : null
      }
    } catch (error) {
      console.error('Error getting character stats:', error)
      return null
    }
  }

  /**
   * Update character appearance
   */
  static async updateCharacterAppearance(characterId: string, color?: string, size?: number): Promise<boolean> {
    try {
      const updateData: any = {
        lastSeen: new Date()
      }

      if (color) updateData.color = color
      if (size) updateData.size = size

      await prisma.character.update({
        where: { id: characterId },
        data: updateData
      })

      return true
    } catch (error) {
      console.error('Error updating character appearance:', error)
      return false
    }
  }

  /**
   * Cleanup offline characters (set offline if last seen > threshold)
   */
  static async cleanupOfflineCharacters(thresholdMinutes: number = 5): Promise<number> {
    try {
      const threshold = new Date(Date.now() - thresholdMinutes * 60 * 1000)
      
      const result = await prisma.character.updateMany({
        where: {
          isOnline: true,
          lastSeen: {
            lt: threshold
          }
        },
        data: {
          isOnline: false
        }
      })

      return result.count
    } catch (error) {
      console.error('Error cleaning up offline characters:', error)
      return 0
    }
  }
}
