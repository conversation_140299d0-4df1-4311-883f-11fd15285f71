import { prisma } from '../database/prisma.js'
import { EntityManager } from '../../../shared/system/EntityManager.js'
import { Entity } from '../../../shared/entity/Entity.js'

export interface RoomConfig {
  name: string
  displayName: string
  mapUrl: string
  maxPlayers?: number
  tickRate?: number
  gameScript?: string
}

export interface WorldState {
  entities: any[]
  timestamp: number
  playerCount: number
}

export class RoomManager {
  private static instance: RoomManager
  private rooms: Map<string, RoomConfig> = new Map()
  private worldStates: Map<string, WorldState> = new Map()

  private constructor() {
    this.initializeDefaultRooms()
  }

  static getInstance(): RoomManager {
    if (!RoomManager.instance) {
      RoomManager.instance = new RoomManager()
    }
    return RoomManager.instance
  }

  /**
   * Initialize default rooms in the database
   */
  private async initializeDefaultRooms() {
    const defaultRooms: RoomConfig[] = [

      {
        name: 'pet-simulator',
        displayName: 'Pet Simulator',
        mapUrl: 'https://notbloxo.fra1.cdn.digitaloceanspaces.com/Notblox-Assets/world/PetWorld.glb',
        maxPlayers: 40,
        tickRate: 20,
        gameScript: 'petSimulatorScript.js'
      }
    ]

    for (const roomConfig of defaultRooms) {
      await this.ensureRoomExists(roomConfig)
      this.rooms.set(roomConfig.name, roomConfig)
    }
  }

  /**
   * Ensure a room exists in the database
   */
  private async ensureRoomExists(config: RoomConfig) {
    try {
      await prisma.room.upsert({
        where: { name: config.name },
        update: {
          displayName: config.displayName,
          mapUrl: config.mapUrl,
          maxPlayers: config.maxPlayers || 50,
          tickRate: config.tickRate || 20,
          gameScript: config.gameScript || 'petSimulatorScript.js'
        },
        create: {
          name: config.name,
          displayName: config.displayName,
          mapUrl: config.mapUrl,
          maxPlayers: config.maxPlayers || 50,
          tickRate: config.tickRate || 20,
          gameScript: config.gameScript || 'petSimulatorScript.js'
        }
      })
    } catch (error) {
      console.error(`Error ensuring room ${config.name} exists:`, error)
    }
  }

  /**
   * Get room configuration
   */
  async getRoomConfig(roomName: string): Promise<RoomConfig | null> {
    try {
      const room = await prisma.room.findUnique({
        where: { name: roomName }
      })

      if (!room) return null

      return {
        name: room.name,
        displayName: room.displayName,
        mapUrl: room.mapUrl,
        maxPlayers: room.maxPlayers,
        tickRate: room.tickRate,
        gameScript: room.gameScript
      }
    } catch (error) {
      console.error(`Error getting room config for ${roomName}:`, error)
      return null
    }
  }

  /**
   * Save world state snapshot
   */
  async saveWorldState(roomName: string, entities: Entity[], playerCount: number) {
    try {
      // Serialize entities for storage
      const serializedEntities = entities.map(entity => ({
        id: entity.id,
        type: entity.type,
        components: entity.getAllComponents().map(component => ({
          type: component.constructor.name,
          data: (component as any).serialize ? (component as any).serialize() : component
        }))
      }))

      const worldState: WorldState = {
        entities: serializedEntities,
        timestamp: Date.now(),
        playerCount
      }

      // Store in memory for quick access
      this.worldStates.set(roomName, worldState)

      // Save to database
      await prisma.roomSnapshot.create({
        data: {
          roomId: roomName,
          worldState: worldState as any,
          playerCount
        }
      })

      // Keep only the last 10 snapshots per room
      const snapshots = await prisma.roomSnapshot.findMany({
        where: { roomId: roomName },
        orderBy: { createdAt: 'desc' },
        skip: 10
      })

      if (snapshots.length > 0) {
        await prisma.roomSnapshot.deleteMany({
          where: {
            id: { in: snapshots.map(s => s.id) }
          }
        })
      }

    } catch (error) {
      console.error(`Error saving world state for room ${roomName}:`, error)
    }
  }

  /**
   * Load latest world state
   */
  async loadWorldState(roomName: string): Promise<WorldState | null> {
    try {
      // Check memory first
      const memoryState = this.worldStates.get(roomName)
      if (memoryState) {
        return memoryState
      }

      // Load from database
      const snapshot = await prisma.roomSnapshot.findFirst({
        where: { roomId: roomName },
        orderBy: { createdAt: 'desc' }
      })

      if (!snapshot) return null

      const worldState = snapshot.worldState as unknown as WorldState
      this.worldStates.set(roomName, worldState)

      return worldState
    } catch (error) {
      console.error(`Error loading world state for room ${roomName}:`, error)
      return null
    }
  }

  /**
   * Get all available rooms
   */
  async getAllRooms(): Promise<RoomConfig[]> {
    try {
      const rooms = await prisma.room.findMany()
      return rooms.map(room => ({
        name: room.name,
        displayName: room.displayName,
        mapUrl: room.mapUrl,
        maxPlayers: room.maxPlayers,
        tickRate: room.tickRate,
        gameScript: room.gameScript
      }))
    } catch (error) {
      console.error('Error getting all rooms:', error)
      return []
    }
  }

  /**
   * Update room configuration
   */
  async updateRoomConfig(roomName: string, updates: Partial<RoomConfig>) {
    try {
      await prisma.room.update({
        where: { name: roomName },
        data: {
          displayName: updates.displayName,
          mapUrl: updates.mapUrl,
          maxPlayers: updates.maxPlayers,
          tickRate: updates.tickRate,
          gameScript: updates.gameScript
        }
      })

      // Update memory cache
      const currentConfig = this.rooms.get(roomName)
      if (currentConfig) {
        this.rooms.set(roomName, { ...currentConfig, ...updates })
      }
    } catch (error) {
      console.error(`Error updating room config for ${roomName}:`, error)
    }
  }

  /**
   * Get room statistics
   */
  async getRoomStats(roomName: string) {
    try {
      const snapshots = await prisma.roomSnapshot.findMany({
        where: { roomId: roomName },
        orderBy: { createdAt: 'desc' },
        take: 24 // Last 24 snapshots for stats
      })

      const totalSnapshots = snapshots.length
      const avgPlayerCount = totalSnapshots > 0 
        ? snapshots.reduce((sum, s) => sum + s.playerCount, 0) / totalSnapshots 
        : 0

      const peakPlayers = totalSnapshots > 0 
        ? Math.max(...snapshots.map(s => s.playerCount)) 
        : 0

      return {
        totalSnapshots,
        avgPlayerCount: Math.round(avgPlayerCount * 100) / 100,
        peakPlayers,
        lastSnapshot: snapshots[0]?.createdAt || null
      }
    } catch (error) {
      console.error(`Error getting room stats for ${roomName}:`, error)
      return null
    }
  }
}
