import { PositionComponent } from '../../../../shared/component/PositionComponent.js'
import { RotationComponent } from '../../../../shared/component/RotationComponent.js'
import { SingleSizeComponent } from '../../../../shared/component/SingleSizeComponent.js'
import { StateComponent } from '../../../../shared/component/StateComponent.js'
import { Entity } from '../../../../shared/entity/Entity.js'
import { EntityManager } from '../../../../shared/system/EntityManager.js'
import {
  SerializedEntityType,
  SerializedStateType,
} from '../../../../shared/network/server/serialized.js'
import { GroundCheckComponent } from '../component/GroundedComponent.js'
import { InputComponent } from '../component/InputComponent.js'
import { NetworkDataComponent } from '../../../../shared/network/NetworkDataComponent.js'
import { WebSocketComponent } from '../component/WebsocketComponent.js'
import { PlayerComponent } from '../../../../shared/component/PlayerComponent.js'
import { DynamicRigidBodyComponent } from '../component/physics/DynamicRigidBodyComponent.js'
import { LockedRotationComponent } from '../component/LockedRotationComponent.js'
import { CapsuleColliderComponent } from '../component/physics/CapsuleColliderComponent.js'
import { ColorComponent } from '../../../../shared/component/ColorComponent.js'
import { ServerMeshComponent } from '../../../../shared/component/ServerMeshComponent.js'
import { TextComponent } from '../../../../shared/component/TextComponent.js'
import { PhysicsPropertiesComponent } from '../component/physics/PhysicsPropertiesComponent.js'
import { CharacterService, CharacterData } from '../../character/CharacterService.js'
import { ProgressionService } from '../../progression/ProgressionService.js'
import { AuthService, AuthUser } from '../../auth/AuthService.js'
import { InventoryService } from '../../inventory/InventoryService.js'

export class Player {
  entity: Entity
  user: AuthUser | null = null
  character: CharacterData | null = null
  joinTime: number = Date.now()

  constructor(private ws: WebSocket, initialX: number, initialY: number, initialZ: number, token?: string) {
    this.entity = EntityManager.createEntity(SerializedEntityType.PLAYER)

    // Initialize with default values, will be updated if authenticated
    this.initializeEntity(initialX, initialY, initialZ, 'Guest ' + this.entity.id)

    // Try to authenticate if token provided
    if (token) {
      this.authenticatePlayer(token)
    }
  }

  private initializeEntity(x: number, y: number, z: number, displayName: string, characterData?: CharacterData) {
    // Tag
    const playerComponent = new PlayerComponent(this.entity.id)
    this.entity.addComponent(playerComponent)

    // Use character data if available, otherwise use provided values
    const positionComponent = new PositionComponent(
      this.entity.id,
      characterData?.positionX ?? x,
      characterData?.positionY ?? y,
      characterData?.positionZ ?? z
    )
    this.entity.addComponent(positionComponent)

    const rotationComponent = new RotationComponent(
      this.entity.id,
      0,
      characterData?.rotationY ?? 1,
      2
    )
    this.entity.addComponent(rotationComponent)

    const sizeComponent = new SingleSizeComponent(
      this.entity.id,
      characterData?.size ?? (1.5 + Math.random())
    )
    this.entity.addComponent(sizeComponent)

    // Player name text on top of the head with offset
    const textComponent = new TextComponent(
      this.entity.id,
      displayName,
      0,
      2,
      0,
      250
    )
    this.entity.addComponent(textComponent)

    this.entity.addComponent(new WebSocketComponent(this.entity.id, this.ws))

    // Components used for rendering by the client
    const colorComponent = new ColorComponent(this.entity.id, `#FFFFFF`)
    this.entity.addComponent(colorComponent)

    const stateComponent = new StateComponent(this.entity.id, SerializedStateType.IDLE)
    this.entity.addComponent(stateComponent)

    const serverMeshComponent = new ServerMeshComponent(
      this.entity.id,
      'http://localhost:4001/karola-angel-idle.glb'
    )
    this.entity.addComponent(serverMeshComponent)

    // Hold input data
    this.entity.addComponent(new InputComponent(this.entity.id))

    // Physics
    this.entity.addComponent(
      new PhysicsPropertiesComponent(this.entity.id, {
        enableCcd: true,
        angularDamping: 1.5,
        mass: 5,
      })
    )
    this.entity.addComponent(new GroundCheckComponent(this.entity.id))
    this.entity.addComponent(new DynamicRigidBodyComponent(this.entity.id))
    this.entity.addComponent(new LockedRotationComponent(this.entity.id))
    this.entity.addComponent(new CapsuleColliderComponent(this.entity.id))

    // Network
    const networkDataComponent = new NetworkDataComponent(this.entity.id, this.entity.type, [
      positionComponent,
      rotationComponent,
      sizeComponent,
      colorComponent,
      stateComponent,
      serverMeshComponent,
      textComponent,
      playerComponent,
    ])

    this.entity.addComponent(networkDataComponent)
  }

  /**
   * Authenticate player with JWT token
   */
  private async authenticatePlayer(token: string) {
    try {
      const user = await AuthService.verifyToken(token)
      if (user) {
        this.user = user
        await this.loadCharacterData()
        await this.awardJoinXP()
      }
    } catch (error) {
      console.error('Error authenticating player:', error)
    }
  }

  /**
   * Load character data from database
   */
  private async loadCharacterData() {
    if (!this.user) return

    try {
      const character = await CharacterService.getOrCreateCharacter(this.user.id, this.user.username)
      if (character) {
        this.character = character
        this.updateEntityFromCharacter()

        // Give starter items to new players
        const inventory = await InventoryService.getUserInventory(this.user.id)
        if (inventory.length === 0) {
          await InventoryService.giveStarterItems(this.user.id)
        }
      }
    } catch (error) {
      console.error('Error loading character data:', error)
    }
  }

  /**
   * Update entity components from character data
   */
  private updateEntityFromCharacter() {
    if (!this.character || !this.user) return

    // Update position
    const positionComponent = this.entity.getComponent(PositionComponent)
    if (positionComponent) {
      positionComponent.x = this.character.positionX
      positionComponent.y = this.character.positionY
      positionComponent.z = this.character.positionZ
      positionComponent.updated = true
    }

    // Update rotation
    const rotationComponent = this.entity.getComponent(RotationComponent)
    if (rotationComponent) {
      rotationComponent.y = this.character.rotationY
      rotationComponent.updated = true
    }

    // Update appearance
    const colorComponent = this.entity.getComponent(ColorComponent)
    if (colorComponent) {
      colorComponent.color = this.character.color
      colorComponent.updated = true
    }

    const sizeComponent = this.entity.getComponent(SingleSizeComponent)
    if (sizeComponent) {
      sizeComponent.size = this.character.size
      sizeComponent.updated = true
    }

    // Update display name
    const textComponent = this.entity.getComponent(TextComponent)
    if (textComponent) {
      textComponent.text = this.user.profile?.displayName || this.user.username
      textComponent.updated = true
    }

    // Update player component name
    const playerComponent = this.entity.getComponent(PlayerComponent)
    if (playerComponent) {
      playerComponent.name = this.user.profile?.displayName || this.user.username
      playerComponent.updated = true
    }
  }

  /**
   * Save current position and state to database
   */
  async saveCharacterData() {
    if (!this.character) return

    const positionComponent = this.entity.getComponent(PositionComponent)
    const rotationComponent = this.entity.getComponent(RotationComponent)
    const colorComponent = this.entity.getComponent(ColorComponent)
    const sizeComponent = this.entity.getComponent(SingleSizeComponent)

    if (positionComponent || rotationComponent || colorComponent || sizeComponent) {
      await CharacterService.updateCharacter(this.character.id, {
        positionX: positionComponent?.x,
        positionY: positionComponent?.y,
        positionZ: positionComponent?.z,
        rotationY: rotationComponent?.y,
        color: colorComponent?.color,
        size: sizeComponent?.size,
        isOnline: true
      })
    }
  }

  /**
   * Set character offline when disconnecting
   */
  async setOffline() {
    if (this.character) {
      await CharacterService.setCharacterOffline(this.character.id)
    }

    // Update play time
    if (this.user) {
      const playTimeSeconds = Math.floor((Date.now() - this.joinTime) / 1000)
      await ProgressionService.updatePlayTime(this.user.id, playTimeSeconds)
    }
  }

  /**
   * Award XP for joining the game
   */
  private async awardJoinXP() {
    if (this.user) {
      await ProgressionService.awardActionXP(this.user.id, 'join_game')
    }
  }

  /**
   * Get player display info
   */
  getDisplayInfo() {
    return {
      id: this.entity.id,
      username: this.user?.username || 'Guest',
      displayName: this.user?.profile?.displayName || this.user?.username || 'Guest',
      level: this.user?.profile?.level || 1,
      isAuthenticated: !!this.user
    }
  }

  /**
   * Award XP for an action
   */
  async awardXP(action: string, customXP?: number, customCoins?: number) {
    if (this.user) {
      if (customXP !== undefined || customCoins !== undefined) {
        return await ProgressionService.addExperience(
          this.user.id,
          customXP || 0,
          customCoins || 0,
          action
        )
      } else {
        return await ProgressionService.awardActionXP(this.user.id, action)
      }
    }
    return null
  }

  /**
   * Get progression stats
   */
  async getProgressionStats() {
    if (this.user) {
      return await ProgressionService.getProgressionStats(this.user.id)
    }
    return null
  }

  /**
   * Spend coins
   */
  async spendCoins(amount: number, reason: string) {
    if (this.user) {
      return await ProgressionService.spendCoins(this.user.id, amount, reason)
    }
    return { success: false, remainingCoins: 0 }
  }

  /**
   * Get player inventory
   */
  async getInventory() {
    if (this.user) {
      return await InventoryService.getUserInventory(this.user.id)
    }
    return []
  }

  /**
   * Add item to inventory
   */
  async addItem(itemType: string, quantity: number = 1, metadata?: any) {
    if (this.user) {
      return await InventoryService.addItem(this.user.id, itemType, quantity, metadata)
    }
    return null
  }

  /**
   * Use an item from inventory
   */
  async useItem(itemType: string) {
    if (this.user) {
      return await InventoryService.useItem(this.user.id, itemType)
    }
    return { success: false }
  }

  /**
   * Check if player has item
   */
  async hasItem(itemType: string, quantity: number = 1) {
    if (this.user) {
      return await InventoryService.hasItem(this.user.id, itemType, quantity)
    }
    return false
  }
}
