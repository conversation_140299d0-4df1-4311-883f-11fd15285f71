import { MessageListComponent, MessageComponent } from '../../../../shared/component/MessageComponent.js'
import { Entity } from '../../../../shared/entity/Entity.js'
import { EntityManager } from '../../../../shared/system/EntityManager.js'
import { EventSystem } from '../../../../shared/system/EventSystem.js'
import { SerializedEntityType } from '../../../../shared/network/server/serialized.js'
import { NetworkDataComponent } from '../../../../shared/network/NetworkDataComponent.js'
import { MessageEvent } from '../component/events/MessageEvent.js'
import { ChatComponent } from '../component/tag/TagChatComponent.js'
import { ChatService } from '../../chat/ChatService.js'

export class Chat {
  entity: Entity
  private roomId: string = 'world-1' // Default room

  constructor(roomId?: string) {
    this.roomId = roomId || 'world-1'
    this.entity = EntityManager.createEntity(SerializedEntityType.CHAT)

    this.entity.addComponent(new ChatComponent(this.entity.id))

    // Initialize chat with persistent messages
    this.initializeChat()
  }

  private async initializeChat() {
    // Load recent messages from database
    const recentMessages = await ChatService.getRecentMessages(this.roomId, 20)

    // Convert to the format expected by MessageListComponent
    const messageList = recentMessages.map(msg => new MessageComponent(this.entity.id, {
      a: msg.username,
      c: msg.content,
      mT: msg.messageType as any,
      ts: msg.createdAt.getTime(),
      tpIds: []
    }))

    const chatListComponent = new MessageListComponent(this.entity.id, messageList)
    this.entity.addComponent(chatListComponent)

    const networkDataComponent = new NetworkDataComponent(this.entity.id, this.entity.type, [
      chatListComponent,
    ])
    this.entity.addComponent(networkDataComponent)

    // Add server startup messages
    await this.addServerMessage(`Server started ${new Date().toLocaleString()}`)
    await this.addServerMessage('Welcome to Lexialia! Chat with other players here.')
  }

  /**
   * Add a server message to chat
   */
  async addServerMessage(content: string) {
    const message = await ChatService.saveMessage({
      roomId: this.roomId,
      username: '🖥️ [SERVER]',
      content,
      messageType: 'system'
    })

    if (message) {
      EventSystem.addEvent(
        new MessageEvent(this.entity.id, message.username, message.content)
      )
    }
  }

  /**
   * Add a user message to chat
   */
  async addUserMessage(userId: string | null, username: string, content: string) {
    // Validate message
    const validation = ChatService.validateMessage(content)
    if (!validation.valid) {
      return { success: false, error: validation.error }
    }

    const message = await ChatService.saveMessage({
      roomId: this.roomId,
      userId: userId || undefined,
      username,
      content,
      messageType: 'user'
    })

    if (message) {
      EventSystem.addEvent(
        new MessageEvent(this.entity.id, message.username, message.content)
      )
      return { success: true, message }
    }

    return { success: false, error: 'Failed to save message' }
  }

  /**
   * Get chat statistics
   */
  async getChatStats() {
    return await ChatService.getRoomChatStats(this.roomId)
  }

  /**
   * Search chat messages
   */
  async searchMessages(query: string) {
    return await ChatService.searchMessages(this.roomId, query)
  }
}
