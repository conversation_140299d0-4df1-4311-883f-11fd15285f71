import {
  App,
  DEDICATED_COMPRESSOR_3KB,
  HttpRequest,
  HttpResponse,
  SSLApp,
  us_listen_socket,
  us_socket_context_t,
} from 'uWebSockets.js'
import { unpack } from 'msgpackr'
import { RateLimiterMemory } from 'rate-limiter-flexible'
import { config } from '../../../../../shared/network/config.js'
import { AuthController } from '../../../auth/AuthController.js'
import { ChatService } from '../../../chat/ChatService.js'
import { ChatRateLimiter } from '../../../chat/ChatRateLimiter.js'
import { HealthController } from '../../../health/HealthController.js'

import { EntityDestroyedEvent } from '../../../../../shared/component/events/EntityDestroyedEvent.js'
import {
  ChatMessage,
  InputMessage,
  ProximityPromptInteractMessage,
  SetPlayerNameMessage,
  ClientMessageType,
  ClientMessage,
} from '../../../../../shared/network/client/index.js'
import {
  ConnectionMessage,
  SerializedMessageType,
  ServerMessageType,
} from '../../../../../shared/network/server/index.js'
import { EventSystem } from '../../../../../shared/system/EventSystem.js'

import { MessageEvent } from '../../component/events/MessageEvent.js'
import { Player } from '../../entity/Player.js'
import { InputProcessingSystem } from '../InputProcessingSystem.js'
import { NetworkSystem } from './NetworkSystem.js'
import { ProximityPromptInteractEvent } from '../../component/events/ProximityPromptInteractEvent.js'
import { TextComponent } from '../../../../../shared/component/TextComponent.js'
import { PlayerComponent } from '../../../../../shared/component/PlayerComponent.js'
import { EntityManager } from '../../../../../shared/system/EntityManager.js'
import { MessageListComponent } from '../../../../../shared/component/MessageComponent.js'
import { ChatComponent } from '../../component/tag/TagChatComponent.js'
import { WebSocketComponent } from '../../component/WebsocketComponent.js'
type MessageHandler = (ws: any, message: any) => void

export class WebsocketSystem {
  private port: number = 8001
  private players: Player[] = []
  private messageHandlers: Map<ClientMessageType, MessageHandler> = new Map()
  private inputProcessingSystem: InputProcessingSystem = new InputProcessingSystem()
  private limiter = new RateLimiterMemory({
    points: 10, // Max 10 points per second
    duration: 1, // Each point expires after 1 second
  })
  private chatRateLimiter: ChatRateLimiter = new ChatRateLimiter({
    maxMessages: 5,
    windowMs: 60000, // 1 minute
    cooldownMs: 1000 // 1 second between messages
  })

  constructor() {
    this.initializeServer()
    this.initializeMessageHandlers()
  }
  private async isRateLimited(ip: string): Promise<boolean> {
    try {
      await this.limiter.consume(ip) // Use a unique identifier for each WebSocket connection
      return false // Not rate limited
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_rejRes) {
      return true // Rate limited
    }
  }

  private initializeServer() {
    const isProduction = process.env.NODE_ENV === 'production'
    const acceptedOrigin: string | undefined = process.env.FRONTEND_URL
    const sslKeyFile: string = process.env.SSL_KEY_FILE || '/etc/letsencrypt/live/npm-3/privkey.pem'
    const sslCertFile: string = process.env.SSL_CERT_FILE || '/etc/letsencrypt/live/npm-3/cert.pem'

    if (isProduction) {
      console.log('NODE_ENV : Running in production mode')
    } else {
      console.log('NODE_ENV : Running in development mode')
    }

    if (acceptedOrigin) {
      console.log('FRONTEND_URL : Only accepting connections from origin:', acceptedOrigin)
    }

    const app = isProduction
      ? SSLApp({
          key_file_name: sslKeyFile,
          cert_file_name: sslCertFile,
        })
      : App()

    // Add health check endpoint
    app.get('/health', (res) => {
      // Get connected players count
      const connectedPlayers = this.players.map(
        (player) => player.entity.getComponent(PlayerComponent)?.name
      )

      // Get message list from MessageListComponent if available
      const chatEntity = EntityManager.getFirstEntityWithComponent(
        EntityManager.getInstance().getAllEntities(),
        ChatComponent
      )
      const messageListComponent = chatEntity?.getComponent(MessageListComponent)
      const messages = messageListComponent?.list

      const healthData = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        game: {
          script: process.env.GAME_SCRIPT || 'Unknown',
          tickrate: config.SERVER_TICKRATE,
        },
        players: connectedPlayers,
        messages: {
          globalChat: messages?.filter(
            ({ messageType }) => messageType === SerializedMessageType.GLOBAL_CHAT
          ),
          targetedChat: messages?.filter(
            ({ messageType }) => messageType === SerializedMessageType.TARGETED_CHAT
          ),
          globalNotification: messages?.filter(
            ({ messageType }) => messageType === SerializedMessageType.GLOBAL_NOTIFICATION
          ),
          targetedNotification: messages?.filter(
            ({ messageType }) => messageType === SerializedMessageType.TARGETED_NOTIFICATION
          ),
        },
      }

      // Send response
      res.writeHeader('Content-Type', 'application/json')
      res.end(JSON.stringify(healthData))
    })

    // Authentication endpoints
    app.options('/api/auth/*', (res, req) => {
      res.onAborted(() => {
        console.log('CORS request aborted')
      })
      AuthController.handleCors(res, req)
    })

    app.post('/api/auth/register', (res, req) => {
      res.onAborted(() => {
        console.log('Register request aborted')
      })
      AuthController.register(res, req).catch(error => {
        console.error('Register error:', error)
        if (!res.aborted) {
          res.writeStatus('500 Internal Server Error')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify({ success: false, error: 'Internal server error' }))
        }
      })
    })

    app.post('/api/auth/login', (res, req) => {
      res.onAborted(() => {
        console.log('Login request aborted')
      })
      AuthController.login(res, req).catch(error => {
        console.error('Login error:', error)
        if (!res.aborted) {
          res.writeStatus('500 Internal Server Error')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify({ success: false, error: 'Internal server error' }))
        }
      })
    })

    app.post('/api/auth/logout', (res, req) => {
      res.onAborted(() => {
        console.log('Logout request aborted')
      })
      AuthController.logout(res, req).catch(error => {
        console.error('Logout error:', error)
        if (!res.aborted) {
          res.writeStatus('500 Internal Server Error')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify({ success: false, error: 'Internal server error' }))
        }
      })
    })

    app.get('/api/auth/profile', (res, req) => {
      res.onAborted(() => {
        console.log('Profile request aborted')
      })
      AuthController.profile(res, req).catch(error => {
        console.error('Profile error:', error)
        if (!res.aborted) {
          res.writeStatus('500 Internal Server Error')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify({ success: false, error: 'Internal server error' }))
        }
      })
    })

    // Health check endpoints
    app.get('/api/health', (res, req) => {
      res.onAborted(() => {
        console.log('Health request aborted')
      })

      HealthController.getSimpleHealth()
        .then(health => {
          res.writeStatus('200 OK')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify(health))
        })
        .catch(error => {
          res.writeStatus('500 Internal Server Error')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify({ status: 'error', error: (error as Error).message }))
        })
    })

    app.get('/api/health/detailed', (res, req) => {
      res.onAborted(() => {
        console.log('Health detailed request aborted')
      })

      HealthController.getHealthStatus()
        .then(health => {
          res.writeStatus('200 OK')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify(health))
        })
        .catch(error => {
          res.writeStatus('500 Internal Server Error')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify({ status: 'error', error: (error as Error).message }))
        })
    })

    app.get('/api/metrics', (res, req) => {
      res.onAborted(() => {
        console.log('Metrics request aborted')
      })

      HealthController.getMetrics()
        .then(metrics => {
          res.writeStatus('200 OK')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify(metrics))
        })
        .catch(error => {
          res.writeStatus('500 Internal Server Error')
          res.writeHeader('Content-Type', 'application/json')
          res.writeHeader('Access-Control-Allow-Origin', '*')
          res.end(JSON.stringify({ status: 'error', error: (error as Error).message }))
        })
    })

    app.ws('/*', {
      idleTimeout: 32,
      maxBackpressure: 1024,
      maxPayloadLength: 512,
      compression: DEDICATED_COMPRESSOR_3KB,
      message: this.onMessage.bind(this),
      open: this.onConnect.bind(this),
      drain: this.onDrain.bind(this),
      close: this.onClose.bind(this),
      upgrade: this.upgradeHandler.bind(this, isProduction, acceptedOrigin),
    })

    app.listen('0.0.0.0', this.port, this.listenHandler.bind(this))
  }
  private upgradeHandler(
    isProduction: boolean,
    acceptedOrigin: string | undefined,
    res: HttpResponse,
    req: HttpRequest,
    context: us_socket_context_t
  ) {
    // Only accept connections from the frontend
    const origin = req.getHeader('origin')
    if (isProduction && acceptedOrigin && origin !== acceptedOrigin) {
      res.writeStatus('403 Forbidden').end()
      return
    }

    res.upgrade(
      {}, // WebSocket handler will go here
      req.getHeader('sec-websocket-key'),
      req.getHeader('sec-websocket-protocol'),
      req.getHeader('sec-websocket-extensions'),
      context
    )
  }

  private listenHandler(listenSocket: us_listen_socket) {
    if (listenSocket) {
      console.log(`WebSocket server listening on port ${this.port}`)
    } else {
      console.error(`Failed to listen on port ${this.port}`)
    }
  }

  private initializeMessageHandlers() {
    this.addMessageHandler(ClientMessageType.INPUT, this.handleInputMessage.bind(this))
    this.addMessageHandler(ClientMessageType.CHAT_MESSAGE, this.handleChatMessage.bind(this))
    this.addMessageHandler(
      ClientMessageType.PROXIMITY_PROMPT_INTERACT,
      this.handleProximityPromptInteractMessage.bind(this)
    )
    this.addMessageHandler(
      ClientMessageType.SET_PLAYER_NAME,
      this.handleSetPlayerNameMessage.bind(this)
    )
  }

  private addMessageHandler(type: ClientMessageType, handler: MessageHandler) {
    this.messageHandlers.set(type, handler)
  }

  private removeMessageHandler(type: ClientMessageType) {
    this.messageHandlers.delete(type)
  }

  private onMessage(ws: any, message: any) {
    const clientMessage: ClientMessage = unpack(message)
    const handler = this.messageHandlers.get(clientMessage.t)
    if (handler) {
      handler(ws, clientMessage)
    }
  }

  // TODO: Create EventOnPlayerConnect and EventOnPlayerDisconnect to respects ECS
  // Might be useful to query the chat and send a message to all players when a player connects or disconnects
  // Also could append scriptable events to be triggered on connect/disconnect depending on the game
  private async onConnect(ws: any) {
    const ipBuffer = ws.getRemoteAddressAsText() as ArrayBuffer
    const ip = Buffer.from(ipBuffer).toString()
    if (await this.isRateLimited(ip)) {
      // Respond to the client indicating that the connection is rate limited
      return ws.close(429, 'Rate limit exceeded')
    }

    // For now, we'll handle auth token through WebSocket messages instead of URL params
    // This is more secure and works better with uWebSockets.js
    const token = undefined // Will be set via authentication message

    const player = new Player(ws, Math.random() * 5, 5, Math.random() * 5, token || undefined)

    const connectionMessage: ConnectionMessage = {
      t: ServerMessageType.FIRST_CONNECTION,
      id: player.entity.id,
      tickRate: config.SERVER_TICKRATE,
    }

    ws.player = player
    ws.send(NetworkSystem.compress(connectionMessage), true)

    // Send welcome message with player info
    const playerInfo = player.getDisplayInfo()
    const welcomeMessage = playerInfo.isAuthenticated
      ? `Welcome back, ${playerInfo.displayName}! (Level ${playerInfo.level})`
      : `Welcome, ${playerInfo.displayName}! Login to save your progress.`

    EventSystem.addEvent(
      new MessageEvent(
        player.entity.id,
        '🖥️ [SERVER]',
        welcomeMessage
      )
    )

    this.players.push(player)
  }

  private onDrain(ws: any) {
    console.log('WebSocket backpressure: ' + ws.getBufferedAmount())
  }

  private async onClose(ws: any) {
    const disconnectedPlayer: Player = ws.player
    if (!disconnectedPlayer) {
      console.error('Disconnect: Player not found?', ws)
      return
    }

    console.log('Disconnect: Player found!')
    const entity = disconnectedPlayer.entity
    const entityId = entity.id

    // Save character data and set offline
    try {
      await disconnectedPlayer.saveCharacterData()
      await disconnectedPlayer.setOffline()
    } catch (error) {
      console.error('Error saving player data on disconnect:', error)
    }

    // Send disconnect message
    const playerInfo = disconnectedPlayer.getDisplayInfo()
    EventSystem.addEvent(
      new MessageEvent(
        entityId,
        '🖥️ [SERVER]',
        `${playerInfo.displayName} left the game`
      )
    )

    EventSystem.addNetworkEvent(new EntityDestroyedEvent(entityId))

    // Remove player from players array
    this.players = this.players.filter((player) => player !== disconnectedPlayer)

    // Remove the WebsocketComponent directly to avoid sending messages to the client
    entity.removeComponent(WebSocketComponent)
  }

  private async handleInputMessage(ws: any, message: InputMessage) {
    const player: Player = ws.player
    if (!player) {
      console.error(`Player with WS ${ws} not found.`)
      return
    }
    const { u: up, d: down, l: left, r: right, s: space, y: angleY, i: interact } = message
    if (
      typeof up !== 'boolean' ||
      typeof down !== 'boolean' ||
      typeof left !== 'boolean' ||
      typeof right !== 'boolean' ||
      typeof space !== 'boolean' ||
      typeof angleY !== 'number' ||
      typeof interact !== 'boolean'
    ) {
      console.error('Invalid input message', message)
      return
    }

    this.inputProcessingSystem.receiveInputPacket(player.entity, message)
  }

  private async handleChatMessage(ws: any, message: ChatMessage) {
    console.log('Chat message received', message)
    const player: Player = ws.player

    const { content } = message
    if (!content || typeof content !== 'string' || content.length === 0) {
      console.error(`Invalid chat message, sent from ${player}`, message)
      return
    }

    const playerInfo = player.getDisplayInfo()
    const playerName = playerInfo.displayName
    const userId = player.user?.id || `guest_${player.entity.id}`

    // Check rate limit
    const rateLimitResult = this.chatRateLimiter.checkRateLimit(userId)
    if (!rateLimitResult.allowed) {
      console.log(`Rate limited message from ${playerName}: ${rateLimitResult.error}`)
      // Send rate limit message back to the player
      EventSystem.addEvent(
        new MessageEvent(
          player.entity.id,
          '🖥️ [SERVER]',
          `⚠️ ${rateLimitResult.error}`
        )
      )
      return
    }

    // Validate message content
    const validation = ChatService.validateMessage(content)
    if (!validation.valid) {
      console.log(`Invalid message from ${playerName}: ${validation.error}`)
      EventSystem.addEvent(
        new MessageEvent(
          player.entity.id,
          '🖥️ [SERVER]',
          `⚠️ ${validation.error}`
        )
      )
      return
    }

    // Save message to database and broadcast
    try {
      const savedMessage = await ChatService.saveMessage({
        roomId: 'world-1', // TODO: Get actual room from player
        userId: player.user?.id,
        username: playerName,
        content,
        messageType: 'user'
      })

      if (savedMessage) {
        // Award XP for sending a message
        await player.awardXP('send_message')

        // Broadcast the message through the event system
        EventSystem.addEvent(new MessageEvent(player.entity.id, playerName, content))

        console.log(`Chat message from ${playerName}: ${content}`)
      }
    } catch (error) {
      console.error('Error handling chat message:', error)
      EventSystem.addEvent(
        new MessageEvent(
          player.entity.id,
          '🖥️ [SERVER]',
          '⚠️ Failed to send message. Please try again.'
        )
      )
    }
  }
  private handleProximityPromptInteractMessage(ws: any, message: ProximityPromptInteractMessage) {
    const player: Player = ws.player
    if (!player) {
      console.error(`Player with WS ${ws} not found.`)
      return
    }
    const { eId } = message
    EventSystem.addEvent(new ProximityPromptInteractEvent(player.entity.id, eId))
  }

  private handleSetPlayerNameMessage(ws: any, message: SetPlayerNameMessage) {
    const player: Player = ws.player
    if (!player) {
      console.error(`Player with WS ${ws} not found.`)
      return
    }

    const { name } = message
    if (!name || typeof name !== 'string') {
      console.error(`Invalid player name message, sent from ${player.entity.id}`, message)
      return
    }

    // Check if player already has a custom name (not the default "Player" name)
    const playerComponent = player.entity.getComponent(PlayerComponent)
    if (playerComponent && !playerComponent.name.startsWith('Player')) {
      console.log(`Player ${playerComponent.name} attempted to change name again. Not allowed.`)
      return
    }

    // Sanitize player name to prevent abuse
    let sanitizedName = name.trim().substring(0, 20)
    // Remove any HTML tags or potentially harmful characters
    sanitizedName = sanitizedName.replace(/<[^>]*>|[<>]/g, '')
    // Remove all spaces from the name
    sanitizedName = sanitizedName.replace(/\s+/g, '')
    // Default to "Player" if name is empty after sanitization
    if (!sanitizedName) sanitizedName = `Player ${player.entity.id}`

    // Check for duplicate names
    const isDuplicateName = this.players.some(
      (p) =>
        p.entity.id !== player.entity.id &&
        p.entity.getComponent(PlayerComponent)?.name === sanitizedName
    )
    if (isDuplicateName) {
      console.log(`Player ${player.entity.id} attempted to use duplicate name: ${sanitizedName}`)
      sanitizedName += `${player.entity.id}`
    }

    // The player component holds the name, but the TextComponent could be altered by game scripts
    // Like : [New Player] - iErcan (10)
    // To not lose the name of the player, store it in the PlayerComponent
    // TODO: Make it more abstract by using a NameComponent.
    // Find the PlayerComponent on the player entity and update it
    if (playerComponent) {
      playerComponent.name = sanitizedName
    } else {
      console.error(`PlayerComponent not found for player ${player.entity.id}`)
    }

    // Find the TextComponent on the player entity and update it
    // Visual update of the name, could be changed in the future because games will alter this
    // This resets the styling of the name
    const textComponent = player.entity.getComponent(TextComponent)
    if (textComponent) {
      textComponent.text = sanitizedName
      // Updated it gets broadcasted + re-rendered
      textComponent.updated = true
      console.log(`Player ${player.entity.id} set name to: ${sanitizedName}`)
    } else {
      console.error(`TextComponent not found for player ${player.entity.id}`)
    }
  }

  getPlayerCount(): number {
    return this.players.length
  }

  getPlayers(): Player[] {
    return this.players
  }
}
