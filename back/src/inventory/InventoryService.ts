import { prisma } from '../database/prisma.js'

export interface InventoryItem {
  id: string
  userId: string
  itemType: string
  itemName: string
  quantity: number
  metadata?: any
  createdAt: Date
  updatedAt: Date
}

export interface ItemDefinition {
  type: string
  name: string
  description: string
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  category: 'tool' | 'cosmetic' | 'consumable' | 'material'
  value: number
  stackable: boolean
  maxStack?: number
}

export class InventoryService {
  private static itemDefinitions: Map<string, ItemDefinition> = new Map()

  /**
   * Initialize item definitions
   */
  static initializeItems() {
    const items: ItemDefinition[] = [
      {
        type: 'starter_tool',
        name: 'Starter Tool',
        description: 'A basic tool for new players',
        rarity: 'common',
        category: 'tool',
        value: 10,
        stackable: false
      },
      {
        type: 'coin_pouch',
        name: 'Coin Pouch',
        description: 'Contains bonus coins',
        rarity: 'uncommon',
        category: 'consumable',
        value: 50,
        stackable: true,
        maxStack: 10
      },
      {
        type: 'xp_boost',
        name: 'XP Boost',
        description: 'Doubles XP gain for 10 minutes',
        rarity: 'rare',
        category: 'consumable',
        value: 100,
        stackable: true,
        maxStack: 5
      },
      {
        type: 'rainbow_skin',
        name: 'Rainbow Skin',
        description: 'A colorful cosmetic skin',
        rarity: 'epic',
        category: 'cosmetic',
        value: 500,
        stackable: false
      },
      {
        type: 'building_block',
        name: 'Building Block',
        description: 'Basic building material',
        rarity: 'common',
        category: 'material',
        value: 5,
        stackable: true,
        maxStack: 64
      }
    ]

    for (const item of items) {
      this.itemDefinitions.set(item.type, item)
    }
  }

  /**
   * Get item definition
   */
  static getItemDefinition(itemType: string): ItemDefinition | null {
    return this.itemDefinitions.get(itemType) || null
  }

  /**
   * Get all item definitions
   */
  static getAllItemDefinitions(): ItemDefinition[] {
    return Array.from(this.itemDefinitions.values())
  }

  /**
   * Add item to user's inventory
   */
  static async addItem(userId: string, itemType: string, quantity: number = 1, metadata?: any): Promise<InventoryItem | null> {
    try {
      const itemDef = this.getItemDefinition(itemType)
      if (!itemDef) {
        console.error(`Unknown item type: ${itemType}`)
        return null
      }

      // Get user's character
      const character = await prisma.character.findFirst({
        where: { userId }
      })

      if (!character) {
        console.error(`No character found for user: ${userId}`)
        return null
      }

      // Check if item is stackable and already exists
      if (itemDef.stackable) {
        const existingItem = await prisma.inventoryItem.findFirst({
          where: {
            characterId: character.id,
            itemType
          }
        })

        if (existingItem) {
          const newQuantity = existingItem.quantity + quantity
          const maxStack = itemDef.maxStack || 999
          
          if (newQuantity <= maxStack) {
            const updatedItem = await prisma.inventoryItem.update({
              where: { id: existingItem.id },
              data: { quantity: newQuantity }
            })

            return {
              id: updatedItem.id,
              userId: character.userId,
              itemType: updatedItem.itemType,
              itemName: itemDef.name,
              quantity: updatedItem.quantity,
              metadata: updatedItem.metadata,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          }
        }
      }

      // Create new item entry
      const newItem = await prisma.inventoryItem.create({
        data: {
          characterId: character.id,
          itemType,
          itemId: itemType, // Use itemType as itemId for now
          quantity,
          metadata: metadata || {}
        }
      })

      return {
        id: newItem.id,
        userId: character.userId,
        itemType: newItem.itemType,
        itemName: itemDef.name,
        quantity: newItem.quantity,
        metadata: newItem.metadata,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    } catch (error) {
      console.error('Error adding item to inventory:', error)
      return null
    }
  }

  /**
   * Remove item from inventory
   */
  static async removeItem(userId: string, itemType: string, quantity: number = 1): Promise<boolean> {
    try {
      // Get user's character
      const character = await prisma.character.findFirst({
        where: { userId }
      })

      if (!character) {
        return false
      }

      const existingItem = await prisma.inventoryItem.findFirst({
        where: {
          characterId: character.id,
          itemType
        }
      })

      if (!existingItem || existingItem.quantity < quantity) {
        return false
      }

      const newQuantity = existingItem.quantity - quantity

      if (newQuantity <= 0) {
        await prisma.inventoryItem.delete({
          where: { id: existingItem.id }
        })
      } else {
        await prisma.inventoryItem.update({
          where: { id: existingItem.id },
          data: { quantity: newQuantity }
        })
      }

      return true
    } catch (error) {
      console.error('Error removing item from inventory:', error)
      return false
    }
  }

  /**
   * Get user's inventory
   */
  static async getUserInventory(userId: string): Promise<InventoryItem[]> {
    try {
      // Get user's character
      const character = await prisma.character.findFirst({
        where: { userId }
      })

      if (!character) {
        return []
      }

      const items = await prisma.inventoryItem.findMany({
        where: { characterId: character.id },
        orderBy: { id: 'asc' }
      })

      return items.map(item => {
        const itemDef = this.getItemDefinition(item.itemType)
        return {
          id: item.id,
          userId: character.userId,
          itemType: item.itemType,
          itemName: itemDef?.name || item.itemType,
          quantity: item.quantity,
          metadata: item.metadata,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    } catch (error) {
      console.error('Error getting user inventory:', error)
      return []
    }
  }

  /**
   * Check if user has item
   */
  static async hasItem(userId: string, itemType: string, quantity: number = 1): Promise<boolean> {
    try {
      // Get user's character
      const character = await prisma.character.findFirst({
        where: { userId }
      })

      if (!character) {
        return false
      }

      const item = await prisma.inventoryItem.findFirst({
        where: {
          characterId: character.id,
          itemType
        }
      })

      return item ? item.quantity >= quantity : false
    } catch (error) {
      console.error('Error checking if user has item:', error)
      return false
    }
  }

  /**
   * Use/consume an item
   */
  static async useItem(userId: string, itemType: string): Promise<{ success: boolean, effect?: any }> {
    try {
      const itemDef = this.getItemDefinition(itemType)
      if (!itemDef) {
        return { success: false }
      }

      const hasItem = await this.hasItem(userId, itemType, 1)
      if (!hasItem) {
        return { success: false }
      }

      // Apply item effect based on type
      let effect: any = null
      
      switch (itemType) {
        case 'coin_pouch':
          effect = { coins: 50 }
          break
        case 'xp_boost':
          effect = { xpMultiplier: 2, duration: 600000 } // 10 minutes
          break
        default:
          effect = { message: `Used ${itemDef.name}` }
      }

      // Remove the item if it's consumable
      if (itemDef.category === 'consumable') {
        await this.removeItem(userId, itemType, 1)
      }

      return { success: true, effect }
    } catch (error) {
      console.error('Error using item:', error)
      return { success: false }
    }
  }

  /**
   * Give starter items to new player
   */
  static async giveStarterItems(userId: string): Promise<void> {
    try {
      await this.addItem(userId, 'starter_tool', 1)
      await this.addItem(userId, 'building_block', 10)
      await this.addItem(userId, 'coin_pouch', 1)
    } catch (error) {
      console.error('Error giving starter items:', error)
    }
  }

  /**
   * Get inventory statistics
   */
  static async getInventoryStats(userId: string) {
    try {
      const items = await this.getUserInventory(userId)
      const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
      const uniqueItems = items.length
      
      const itemsByCategory = items.reduce((acc, item) => {
        const itemDef = this.getItemDefinition(item.itemType)
        const category = itemDef?.category || 'unknown'
        acc[category] = (acc[category] || 0) + item.quantity
        return acc
      }, {} as Record<string, number>)

      const totalValue = items.reduce((sum, item) => {
        const itemDef = this.getItemDefinition(item.itemType)
        return sum + (itemDef?.value || 0) * item.quantity
      }, 0)

      return {
        totalItems,
        uniqueItems,
        itemsByCategory,
        totalValue
      }
    } catch (error) {
      console.error('Error getting inventory stats:', error)
      return null
    }
  }
}

// Initialize items when the service is loaded
InventoryService.initializeItems()
