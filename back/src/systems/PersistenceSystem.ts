import { Entity } from '../../../shared/entity/Entity.js'
import { PlayerComponent } from '../../../shared/component/PlayerComponent.js'
import { EntityManager } from '../../../shared/system/EntityManager.js'
import { RoomManager } from '../rooms/RoomManager.js'
import { CharacterService } from '../character/CharacterService.js'

export class PersistenceSystem {
  private lastSaveTime: number = Date.now()
  private saveInterval: number = 30000 // Save every 30 seconds
  private lastWorldStateSave: number = Date.now()
  private worldStateSaveInterval: number = 300000 // Save world state every 5 minutes

  /**
   * Update persistence system
   */
  update(entities: Entity[]) {
    const currentTime = Date.now()

    // Save player data periodically
    if (currentTime - this.lastSaveTime >= this.saveInterval) {
      this.savePlayerData(entities)
      this.lastSaveTime = currentTime
    }

    // Save world state periodically
    if (currentTime - this.lastWorldStateSave >= this.worldStateSaveInterval) {
      this.saveWorldState(entities)
      this.lastWorldStateSave = currentTime
    }
  }

  /**
   * Save all player character data
   */
  private async savePlayerData(entities: Entity[]) {
    const playerEntities = entities.filter(entity => entity.getComponent(PlayerComponent))
    
    for (const entity of playerEntities) {
      try {
        // Get the player instance from the WebSocket system
        // This is a bit of a hack, but we need access to the Player instance
        // In a real implementation, you might want to store player references differently
        const playerComponent = entity.getComponent(PlayerComponent)
        if (playerComponent && playerComponent.name !== 'Guest') {
          // Only save authenticated players
          // The actual saving is handled by the Player class when needed
        }
      } catch (error) {
        console.error('Error saving player data:', error)
      }
    }
  }

  /**
   * Save world state snapshot
   */
  private async saveWorldState(entities: Entity[]) {
    try {
      const roomManager = RoomManager.getInstance()
      const playerCount = entities.filter(entity => entity.getComponent(PlayerComponent)).length
      
      // Save world state for the current room (assuming single room for now)
      await roomManager.saveWorldState('world-1', entities, playerCount)
      
      console.log(`World state saved with ${playerCount} players`)
    } catch (error) {
      console.error('Error saving world state:', error)
    }
  }

  /**
   * Cleanup offline characters
   */
  async cleanupOfflineCharacters() {
    try {
      const cleanedCount = await CharacterService.cleanupOfflineCharacters(5) // 5 minutes threshold
      if (cleanedCount > 0) {
        console.log(`Cleaned up ${cleanedCount} offline characters`)
      }
    } catch (error) {
      console.error('Error cleaning up offline characters:', error)
    }
  }

  /**
   * Force save all data
   */
  async forceSave(entities: Entity[]) {
    await this.savePlayerData(entities)
    await this.saveWorldState(entities)
    await this.cleanupOfflineCharacters()
  }
}
