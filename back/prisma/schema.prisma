// Lexialia MMORPG Database Schema
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User authentication and account management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String   // bcrypt hashed
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // User profile and game data
  profile   UserProfile?
  characters Character[]
  sessions  UserSession[]

  @@map("users")
}

// Extended user profile information
model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  displayName String?
  avatar      String?  // URL to avatar image
  bio         String?
  level       Int      @default(1)
  experience  Int      @default(0)
  coins       Int      @default(100)

  // Game statistics
  totalPlayTime    Int @default(0) // in seconds
  gamesPlayed      Int @default(0)
  lastActiveAt     DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// User session management for JWT tokens
model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// Character/Player data for in-game representation
model Character {
  id       String @id @default(cuid())
  userId   String
  name     String

  // Position and appearance
  positionX Float @default(0)
  positionY Float @default(5)
  positionZ Float @default(0)
  rotationY Float @default(0)

  // Character customization
  color     String @default("#3b82f6")
  size      Float  @default(1.5)

  // Game state
  currentRoom String @default("world-1")
  isOnline    Boolean @default(false)
  lastSeen    DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  inventoryItems InventoryItem[]

  @@map("characters")
}

// Persistent room/world state
model Room {
  id          String   @id @default(cuid())
  name        String   @unique
  displayName String
  mapUrl      String
  maxPlayers  Int      @default(50)

  // Room configuration
  tickRate    Int      @default(20)
  gameScript  String   @default("defaultScript.js")

  // Persistence
  worldState  Json?    // Serialized world state
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  snapshots RoomSnapshot[]

  @@map("rooms")
}

// Room state snapshots for persistence
model RoomSnapshot {
  id        String   @id @default(cuid())
  roomId    String
  worldState Json    // Serialized ECS state
  playerCount Int    @default(0)
  createdAt DateTime @default(now())

  room Room @relation(fields: [roomId], references: [id], onDelete: Cascade)

  @@map("room_snapshots")
}

// Chat message history
model ChatMessage {
  id        String   @id @default(cuid())
  roomId    String
  userId    String?  // null for system messages
  username  String
  content   String
  messageType String @default("user") // user, system, admin
  createdAt DateTime @default(now())

  @@map("chat_messages")
}

// Basic inventory system
model InventoryItem {
  id          String @id @default(cuid())
  characterId String
  itemType    String // "weapon", "tool", "consumable", etc.
  itemId      String // identifier for the item
  quantity    Int    @default(1)
  metadata    Json?  // additional item properties

  character Character @relation(fields: [characterId], references: [id], onDelete: Cascade)

  @@map("inventory_items")
}
