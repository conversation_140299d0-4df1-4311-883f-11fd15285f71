{"name": "lexialia.online", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"debug": "tsc-watch --onSuccess \"node --inspect=7000  dist/back/src/sandbox.js\"", "dev": "tsc-watch --onSuccess \"node  dist/back/src/index.js\"", "build": "tsc --build tsconfig.json", "start": "node dist/back/src/index.js", "lint": "eslint src/**/*.ts", "format": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "keywords": [], "author": "iercann", "license": "ISC", "devDependencies": {"@eslint/js": "^9.3.0", "@jest/globals": "^30.1.2", "@types/jest": "^30.0.0", "@types/uws": "^0.13.6", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "dotenv": "^17.2.2", "eslint": "^8.57.0", "globals": "^15.3.0", "jest": "^30.1.3", "ts-jest": "^29.4.4", "tsc-watch": "^6.2.0", "tsconfig-paths": "^4.2.0", "typescript-eslint": "^8.29.0"}, "type": "module", "dependencies": {"@dimforge/rapier3d-compat": "^0.14.0", "@prisma/client": "^6.16.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.10.1", "@types/pako": "^2.0.3", "@types/three": "^0.172.0", "bcryptjs": "^3.0.2", "bufferutil": "^4.0.9", "jsonwebtoken": "^9.0.2", "msgpackr": "^1.9.9", "node-three-gltf": "^1.8.3", "pako": "^2.1.0", "prisma": "^6.16.2", "rate-limiter-flexible": "^5.0.3", "three": "^0.172.0", "typescript": "^5.7.3", "utf-8-validate": "^6.0.5", "uWebSockets.js": "github:uNetworking/uWebSockets.js#binaries"}}