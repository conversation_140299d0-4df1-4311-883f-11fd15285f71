import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'
import { AuthService } from '../src/auth/AuthService.js'
import { prisma } from '../src/database/prisma.js'

describe('AuthService', () => {
  beforeAll(async () => {
    // Setup test database
    await prisma.$connect()
  })

  afterAll(async () => {
    // Cleanup test database
    await prisma.user.deleteMany({})
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Clean up users before each test
    await prisma.user.deleteMany({})
  })

  describe('registerUser', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      }

      const result = await AuthService.registerUser(userData)

      expect(result.success).toBe(true)
      expect(result.user).toBeDefined()
      expect(result.user.username).toBe(userData.username)
      expect(result.user.email).toBe(userData.email)
      expect(result.token).toBeDefined()
    })

    it('should not register user with duplicate username', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      }

      // Register first user
      await AuthService.registerUser(userData)

      // Try to register with same username
      const result = await AuthService.registerUser({
        ...userData,
        email: '<EMAIL>'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('already exists')
    })

    it('should not register user with duplicate email', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      }

      // Register first user
      await AuthService.registerUser(userData)

      // Try to register with same email
      const result = await AuthService.registerUser({
        ...userData,
        username: 'differentuser'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('already exists')
    })
  })

  describe('loginUser', () => {
    beforeEach(async () => {
      // Create a test user
      await AuthService.registerUser({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      })
    })

    it('should login with correct credentials', async () => {
      const result = await AuthService.loginUser('testuser', 'password123')

      expect(result.success).toBe(true)
      expect(result.user).toBeDefined()
      expect(result.user.username).toBe('testuser')
      expect(result.token).toBeDefined()
    })

    it('should not login with incorrect password', async () => {
      const result = await AuthService.loginUser('testuser', 'wrongpassword')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid')
    })

    it('should not login with non-existent user', async () => {
      const result = await AuthService.loginUser('nonexistent', 'password123')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid')
    })
  })

  describe('verifyToken', () => {
    let validToken

    beforeEach(async () => {
      // Create a test user and get token
      const result = await AuthService.registerUser({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      })
      validToken = result.token
    })

    it('should verify valid token', async () => {
      const user = await AuthService.verifyToken(validToken)

      expect(user).toBeDefined()
      expect(user.username).toBe('testuser')
    })

    it('should not verify invalid token', async () => {
      const user = await AuthService.verifyToken('invalid-token')

      expect(user).toBeNull()
    })
  })
})
