import { config } from 'dotenv'

// Load test environment variables
config({ path: '.env.test' })

// Set test environment
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://test_user:test_password@localhost:5432/test_db?schema=public'
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret-key'

// Global test setup
global.beforeAll = global.beforeAll || (() => {})
global.afterAll = global.afterAll || (() => {})

// Increase timeout for database operations
jest.setTimeout(30000)
