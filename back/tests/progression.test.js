import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'
import { ProgressionService } from '../src/progression/ProgressionService.js'
import { AuthService } from '../src/auth/AuthService.js'
import { prisma } from '../src/database/prisma.js'

describe('ProgressionService', () => {
  let testUser

  beforeAll(async () => {
    await prisma.$connect()
  })

  afterAll(async () => {
    // Cleanup
    await prisma.userProfile.deleteMany({})
    await prisma.user.deleteMany({})
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Clean up and create test user
    await prisma.userProfile.deleteMany({})
    await prisma.user.deleteMany({})
    
    const result = await AuthService.registerUser({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    })
    testUser = result.user
  })

  describe('XP and Level Calculations', () => {
    it('should calculate correct XP requirements for levels', () => {
      expect(ProgressionService.getXPRequiredForLevel(1)).toBe(0)
      expect(ProgressionService.getXPRequiredForLevel(2)).toBe(100)
      expect(ProgressionService.getXPRequiredForLevel(3)).toBe(225)
      expect(ProgressionService.getXPRequiredForLevel(4)).toBe(375)
    })

    it('should calculate correct level from XP', () => {
      expect(ProgressionService.getLevelFromXP(0)).toBe(1)
      expect(ProgressionService.getLevelFromXP(100)).toBe(2)
      expect(ProgressionService.getLevelFromXP(225)).toBe(3)
      expect(ProgressionService.getLevelFromXP(300)).toBe(3)
      expect(ProgressionService.getLevelFromXP(375)).toBe(4)
    })
  })

  describe('addExperience', () => {
    it('should add experience and handle level up', async () => {
      const result = await ProgressionService.addExperience(testUser.id, 150, 10, 'test')

      expect(result.experienceGained).toBe(150)
      expect(result.coinsGained).toBe(60) // 10 + 50 for level up
      expect(result.leveledUp).toBe(true)
      expect(result.newLevel).toBe(2)
      expect(result.totalExperience).toBe(150)
    })

    it('should add experience without level up', async () => {
      const result = await ProgressionService.addExperience(testUser.id, 50, 5, 'test')

      expect(result.experienceGained).toBe(50)
      expect(result.coinsGained).toBe(5)
      expect(result.leveledUp).toBe(false)
      expect(result.newLevel).toBe(1)
      expect(result.totalExperience).toBe(50)
    })
  })

  describe('awardActionXP', () => {
    it('should award correct XP for join_game action', async () => {
      const result = await ProgressionService.awardActionXP(testUser.id, 'join_game')

      expect(result).toBeDefined()
      expect(result.experienceGained).toBe(10)
      expect(result.coinsGained).toBe(5)
    })

    it('should award correct XP for win_game action', async () => {
      const result = await ProgressionService.awardActionXP(testUser.id, 'win_game')

      expect(result).toBeDefined()
      expect(result.experienceGained).toBe(100)
      expect(result.coinsGained).toBe(50)
    })

    it('should return null for unknown action', async () => {
      const result = await ProgressionService.awardActionXP(testUser.id, 'unknown_action')

      expect(result).toBeNull()
    })
  })

  describe('coins management', () => {
    beforeEach(async () => {
      // Give user some coins
      await ProgressionService.addCoins(testUser.id, 100, 'test setup')
    })

    it('should add coins successfully', async () => {
      const newTotal = await ProgressionService.addCoins(testUser.id, 50, 'test')

      expect(newTotal).toBe(150)
    })

    it('should spend coins successfully', async () => {
      const result = await ProgressionService.spendCoins(testUser.id, 30, 'test purchase')

      expect(result.success).toBe(true)
      expect(result.remainingCoins).toBe(70)
    })

    it('should not spend more coins than available', async () => {
      const result = await ProgressionService.spendCoins(testUser.id, 150, 'expensive item')

      expect(result.success).toBe(false)
      expect(result.remainingCoins).toBe(100)
    })
  })

  describe('getProgressionStats', () => {
    beforeEach(async () => {
      // Set up user with some progression
      await ProgressionService.addExperience(testUser.id, 150, 50, 'test')
    })

    it('should return correct progression stats', async () => {
      const stats = await ProgressionService.getProgressionStats(testUser.id)

      expect(stats).toBeDefined()
      expect(stats.level).toBe(2)
      expect(stats.totalExperience).toBe(150)
      expect(stats.xpProgress.current).toBe(50) // 150 - 100 (XP for level 2)
      expect(stats.xpProgress.needed).toBe(125) // XP needed for level 3 - XP for level 2
      expect(stats.nextLevel.level).toBe(3)
    })
  })

  describe('getLeaderboard', () => {
    beforeEach(async () => {
      // Create multiple users with different levels
      const users = []
      for (let i = 0; i < 3; i++) {
        const result = await AuthService.registerUser({
          username: `user${i}`,
          email: `user${i}@example.com`,
          password: 'password123'
        })
        users.push(result.user)
        
        // Give different amounts of XP
        await ProgressionService.addExperience(result.user.id, i * 100, 0, 'test')
      }
    })

    it('should return leaderboard ordered by level and XP', async () => {
      const leaderboard = await ProgressionService.getLeaderboard(5)

      expect(leaderboard).toBeDefined()
      expect(leaderboard.length).toBeGreaterThan(0)
      
      // Check that it's ordered correctly (highest level/XP first)
      for (let i = 1; i < leaderboard.length; i++) {
        const current = leaderboard[i]
        const previous = leaderboard[i - 1]
        
        expect(
          previous.level > current.level || 
          (previous.level === current.level && previous.experience >= current.experience)
        ).toBe(true)
      }
    })
  })
})
