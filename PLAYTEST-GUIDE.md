# Lexialia MMORPG Playtest Guide

## Quick Start

### Prerequisites
- Node.js 18+ installed
- Docker and <PERSON><PERSON> Compose installed
- Git for cloning the repository

### Setup Instructions

1. **Clone and Setup**
```bash
git clone <repository-url>
cd lexialia-v2
```

2. **Environment Configuration**
```bash
# Backend environment
cp back/.env.example back/.env
# Edit back/.env with your configuration

# Frontend environment  
cp front/.env.example front/.env.local
# Edit front/.env.local with your configuration
```

3. **Start with Docker (Recommended)**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

4. **Manual Setup (Development)**
```bash
# Start PostgreSQL
docker-compose up -d postgres

# Setup backend
cd back
npm install
npx prisma generate
npx prisma migrate dev
npm run dev

# Setup frontend (new terminal)
cd front
npm install
npm run dev
```

### Access Points
- **Game Frontend**: http://localhost:4000
- **Backend API**: http://localhost:8001
- **Health Check**: http://localhost:8001/api/health
- **Nginx Proxy Manager**: http://localhost:81 (admin/changeme)

## Testing Scenarios

### 1. User Registration and Authentication

#### Test Case: New User Registration
1. Navigate to http://localhost:4000
2. Click "Sign Up" or register button
3. Fill in registration form:
   - Username: `testuser1`
   - Email: `<EMAIL>`
   - Password: `password123`
4. Submit form
5. **Expected**: User is registered and automatically logged in
6. **Verify**: User profile shows level 1, 0 XP, starter coins

#### Test Case: User Login
1. Use existing credentials or register first
2. Click "Login" 
3. Enter credentials
4. **Expected**: Successful login with user profile loaded
5. **Verify**: Character spawns in game world with saved position

#### Test Case: Guest Play
1. Navigate to game without logging in
2. **Expected**: Can play as guest with temporary character
3. **Verify**: No progression saved, shows as "Guest" in chat

### 2. Game World and Movement

#### Test Case: Character Movement
1. Login and enter game world
2. Use WASD keys to move character
3. Use mouse to look around
4. **Expected**: Smooth character movement with physics
5. **Verify**: Position updates in real-time, other players see movement

#### Test Case: World Loading
1. Enter game world
2. **Expected**: 3D world loads with TestWorld.glb map
3. **Verify**: Textures load, lighting works, no console errors

#### Test Case: Physics and Collision
1. Move character around the world
2. Try to walk through walls/objects
3. Jump and test gravity
4. **Expected**: Proper collision detection and physics response
5. **Verify**: Character cannot pass through solid objects

### 3. Multiplayer Features

#### Test Case: Multiple Players
1. Open game in multiple browser tabs/windows
2. Login with different accounts in each
3. Move characters around
4. **Expected**: All players see each other in real-time
5. **Verify**: Smooth synchronization, no lag or stuttering

#### Test Case: Player Identification
1. With multiple players connected
2. **Expected**: Each player shows username above character
3. **Verify**: Different colors/appearances for different players

### 4. Chat System

#### Test Case: Basic Chat
1. Login and enter game
2. Type message in chat box
3. Press Enter to send
4. **Expected**: Message appears in chat for all players
5. **Verify**: Username and timestamp shown correctly

#### Test Case: Chat Persistence
1. Send several chat messages
2. Refresh browser or reconnect
3. **Expected**: Recent chat history loads on reconnect
4. **Verify**: Messages persist across sessions

#### Test Case: Chat Rate Limiting
1. Send messages rapidly (more than 5 per minute)
2. **Expected**: Rate limit warning appears
3. **Verify**: Excessive messages are blocked

#### Test Case: Chat Validation
1. Try sending empty message
2. Try sending very long message (>200 characters)
3. **Expected**: Validation errors shown
4. **Verify**: Invalid messages are rejected

### 5. Progression System

#### Test Case: XP Gain
1. Login and play for a few minutes
2. Send chat messages
3. Move around the world
4. **Expected**: XP notifications appear
5. **Verify**: XP bar updates, level progress shown

#### Test Case: Level Up
1. Gain enough XP to level up (100 XP for level 2)
2. **Expected**: Level up notification with coin bonus
3. **Verify**: Level increases, bonus coins awarded

#### Test Case: Daily Login Bonus
1. Login for the first time in a day
2. **Expected**: Daily login bonus XP awarded
3. **Verify**: Bonus appears in progression notifications

### 6. Inventory System

#### Test Case: Starter Items
1. Register new account and login
2. **Expected**: Starter items automatically added to inventory
3. **Verify**: Starter tool, building blocks, coin pouch present

#### Test Case: Item Usage
1. Use a consumable item (coin pouch)
2. **Expected**: Item effect applied, item removed from inventory
3. **Verify**: Coins increased, item quantity decreased

### 7. Persistence and Data

#### Test Case: Character Persistence
1. Move character to specific location
2. Change appearance if possible
3. Logout and login again
4. **Expected**: Character spawns at last saved position
5. **Verify**: All character data preserved

#### Test Case: Progression Persistence
1. Gain XP and level up
2. Earn coins
3. Logout and login again
4. **Expected**: All progression data preserved
5. **Verify**: Level, XP, coins match previous session

### 8. Performance Testing

#### Test Case: Connection Stability
1. Play for extended period (10+ minutes)
2. **Expected**: Stable connection, no disconnects
3. **Verify**: No memory leaks, consistent performance

#### Test Case: Multiple Concurrent Users
1. Have 5+ users connect simultaneously
2. All users move and chat actively
3. **Expected**: Smooth performance for all users
4. **Verify**: No lag spikes, messages delivered promptly

## Common Issues and Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Reset database if needed
cd back
npx prisma migrate reset
npx prisma generate
```

### Frontend Build Issues
```bash
# Clear Next.js cache
cd front
rm -rf .next
npm run build
```

### WebSocket Connection Issues
```bash
# Check backend logs
docker-compose logs game_backend

# Verify WebSocket endpoint
curl -I http://localhost:8001/health
```

### Performance Issues
```bash
# Check system resources
docker stats

# Monitor backend performance
curl http://localhost:8001/api/metrics
```

## Test Data and Scenarios

### Sample User Accounts
Create these test accounts for comprehensive testing:

1. **Admin User**
   - Username: `admin`
   - Email: `<EMAIL>`
   - Use for testing admin features

2. **Regular Users**
   - Username: `player1`, `player2`, `player3`
   - Email: `<EMAIL>`, etc.
   - Use for multiplayer testing

3. **Guest Testing**
   - No account needed
   - Test guest functionality

### Test Scenarios by User Type

#### New User Journey
1. Registration → Tutorial → First gameplay → Progression
2. Focus on onboarding experience and starter content

#### Returning User Journey  
1. Login → Character loads → Continue progression
2. Focus on data persistence and continued engagement

#### Multiplayer Interaction
1. Multiple users in same world
2. Chat communication
3. Shared world state

## Reporting Issues

### Bug Report Template
```
**Bug Description**: Brief description of the issue

**Steps to Reproduce**:
1. Step one
2. Step two
3. Step three

**Expected Behavior**: What should happen

**Actual Behavior**: What actually happens

**Environment**:
- Browser: Chrome/Firefox/Safari version
- OS: Windows/Mac/Linux
- Account Type: Guest/Registered

**Console Errors**: Any JavaScript console errors

**Additional Info**: Screenshots, logs, etc.
```

### Performance Report Template
```
**Performance Issue**: Description of performance problem

**Metrics**:
- FPS: Average frames per second
- Latency: Network latency to server
- Memory Usage: Browser memory consumption
- Load Time: Time to load game world

**Conditions**:
- Number of concurrent players
- Duration of gameplay session
- Specific actions causing issues

**System Specs**:
- CPU, RAM, GPU information
- Network connection speed
```

## Success Criteria

### Core Functionality ✅
- [ ] User registration and authentication works
- [ ] Character movement and physics work correctly
- [ ] Multiplayer synchronization is smooth
- [ ] Chat system functions properly
- [ ] Progression system awards XP and levels
- [ ] Data persistence works across sessions

### Performance Benchmarks ✅
- [ ] Game loads within 10 seconds
- [ ] Supports 10+ concurrent users smoothly
- [ ] Chat messages delivered within 100ms
- [ ] Character movement latency < 50ms
- [ ] No memory leaks during extended play

### User Experience ✅
- [ ] Intuitive controls and interface
- [ ] Clear feedback for all actions
- [ ] Responsive design works on different screen sizes
- [ ] Error messages are helpful and clear
- [ ] Onboarding process is smooth for new users
