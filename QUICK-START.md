# 🚀 Quick Start Guide - Lexialia MMORPG

## Docker Compatibility Issue Fix

If you're experiencing Docker compatibility issues, here are multiple ways to get the MMORPG running:

## Option 1: Simple Database-Only Docker (Recommended)

```bash
# Start only PostgreSQL with Docker
docker-compose -f docker-compose.dev.yml up -d

# Verify PostgreS<PERSON> is running
docker-compose -f docker-compose.dev.yml ps
```

Then run the backend and frontend manually:

```bash
# Terminal 1: Backend
cd back
npm install
cp .env.example .env
npx prisma generate
npx prisma migrate dev
npm run dev

# Terminal 2: Frontend (new terminal)
cd front
npm install
cp .env.example .env.local
npm run dev
```

## Option 2: Manual Setup (No Docker)

If Docker is causing issues, you can run everything manually:

### 1. Install PostgreSQL locally

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE lexialia_db;
CREATE USER lexialia WITH PASSWORD 'lexialia_password';
GRANT ALL PRIVILEGES ON DATABASE lexialia_db TO lexialia;
\q
```

**macOS (with Homebrew):**
```bash
brew install postgresql
brew services start postgresql
createdb lexialia_db
```

### 2. Setup Backend

```bash
cd back
npm install

# Create .env file
cat > .env << EOF
DATABASE_URL="postgresql://lexialia:lexialia_password@localhost:5432/lexialia_db?schema=public"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
NODE_ENV="development"
GAME_TICKRATE=20
EOF

# Setup database
npx prisma generate
npx prisma migrate dev

# Start backend
npm run dev
```

### 3. Setup Frontend

```bash
cd front
npm install

# Create .env.local file
cat > .env.local << EOF
NEXT_PUBLIC_SERVER_URL=ws://localhost:8001
NEXT_PUBLIC_API_URL=http://localhost:8001
EOF

# Start frontend
npm run dev
```

## Option 3: Fix Docker Compose Version

If you want to use Docker, try updating docker-compose:

```bash
# Remove old docker-compose
sudo apt remove docker-compose

# Install newer version
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify version
docker-compose --version

# Try again
docker-compose -f docker-compose.dev.yml up -d
```

## Option 4: Use Docker Compose V2

```bash
# Try with docker compose (V2 syntax)
docker compose -f docker-compose.dev.yml up -d
```

## Verification Steps

Once you have the services running, verify everything works:

### 1. Check Backend Health
```bash
curl http://localhost:8001/api/health
# Should return: {"status":"ok","timestamp":"..."}
```

### 2. Check Database Connection
```bash
cd back
npx prisma studio
# Opens database browser at http://localhost:5555
```

### 3. Access the Game
Open your browser and go to: http://localhost:4000

You should see the Lexialia MMORPG login screen!

## Troubleshooting

### Backend Issues
```bash
# Check backend logs
cd back
npm run dev

# If database connection fails, check PostgreSQL
sudo systemctl status postgresql  # Linux
brew services list | grep postgres  # macOS
```

### Frontend Issues
```bash
# Check frontend logs
cd front
npm run dev

# Clear Next.js cache if needed
rm -rf .next
npm run dev
```

### Database Issues
```bash
# Reset database if needed
cd back
npx prisma migrate reset
npx prisma generate
npx prisma migrate dev
```

### Port Conflicts
If ports 4000 or 8001 are in use:

```bash
# Check what's using the ports
sudo lsof -i :4000
sudo lsof -i :8001

# Kill processes if needed
sudo kill -9 <PID>
```

## Environment Variables

### Backend (.env)
```env
DATABASE_URL="postgresql://lexialia:lexialia_password@localhost:5432/lexialia_db?schema=public"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
NODE_ENV="development"
GAME_TICKRATE=20
```

### Frontend (.env.local)
```env
NEXT_PUBLIC_SERVER_URL=ws://localhost:8001
NEXT_PUBLIC_API_URL=http://localhost:8001
```

## Success Indicators

✅ **Backend Running**: http://localhost:8001/api/health returns `{"status":"ok"}`
✅ **Frontend Running**: http://localhost:4000 shows the game interface
✅ **Database Connected**: No database errors in backend logs
✅ **WebSocket Working**: Real-time features work in the game

## Next Steps

Once everything is running:

1. **Register a new account** or **play as guest**
2. **Explore the 3D world** with WASD movement
3. **Chat with other players** (open multiple browser tabs to test)
4. **Check your progression** (XP, levels, coins)
5. **View your inventory** and use starter items

## Need Help?

If you're still having issues:

1. Check the logs in both backend and frontend terminals
2. Verify all environment variables are set correctly
3. Make sure PostgreSQL is running and accessible
4. Try the manual setup option if Docker continues to cause problems

The MMORPG transformation is complete and ready to play! 🎮✨
