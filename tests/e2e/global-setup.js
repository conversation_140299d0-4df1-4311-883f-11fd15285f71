/**
 * Global setup for E2E tests
 * Runs before all tests to prepare the test environment
 */

import { execSync } from 'child_process'
import { chromium } from '@playwright/test'

async function globalSetup() {
  console.log('🚀 Setting up E2E test environment...')

  try {
    // Start PostgreSQL if not running
    console.log('📦 Starting PostgreSQL database...')
    execSync('docker-compose up -d postgres', { stdio: 'inherit' })
    
    // Wait for database to be ready
    console.log('⏳ Waiting for database to be ready...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    // Setup test database
    console.log('🗄️ Setting up test database...')
    execSync('cd back && npx prisma generate', { stdio: 'inherit' })
    execSync('cd back && npx prisma db push --force-reset', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        DATABASE_URL: 'postgresql://test_user:test_password@localhost:5432/test_db?schema=public'
      }
    })

    // Verify backend health
    console.log('🔍 Verifying backend health...')
    const browser = await chromium.launch()
    const page = await browser.newPage()
    
    let healthCheck = false
    let attempts = 0
    const maxAttempts = 30
    
    while (!healthCheck && attempts < maxAttempts) {
      try {
        const response = await page.request.get('http://localhost:8001/api/health')
        if (response.ok()) {
          const data = await response.json()
          if (data.status === 'ok') {
            healthCheck = true
            console.log('✅ Backend health check passed')
          }
        }
      } catch (error) {
        // Backend not ready yet
      }
      
      if (!healthCheck) {
        attempts++
        console.log(`⏳ Waiting for backend... (${attempts}/${maxAttempts})`)
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
    
    await browser.close()
    
    if (!healthCheck) {
      throw new Error('Backend failed to start within timeout period')
    }

    // Verify frontend
    console.log('🔍 Verifying frontend...')
    const frontendBrowser = await chromium.launch()
    const frontendPage = await frontendBrowser.newPage()
    
    let frontendReady = false
    attempts = 0
    
    while (!frontendReady && attempts < maxAttempts) {
      try {
        await frontendPage.goto('http://localhost:4000', { timeout: 5000 })
        frontendReady = true
        console.log('✅ Frontend health check passed')
      } catch (error) {
        attempts++
        console.log(`⏳ Waiting for frontend... (${attempts}/${maxAttempts})`)
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
    
    await frontendBrowser.close()
    
    if (!frontendReady) {
      throw new Error('Frontend failed to start within timeout period')
    }

    console.log('🎉 E2E test environment ready!')
    
  } catch (error) {
    console.error('❌ Failed to setup E2E test environment:', error)
    throw error
  }
}

export default globalSetup
