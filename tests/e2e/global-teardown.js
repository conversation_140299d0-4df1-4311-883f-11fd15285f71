/**
 * Global teardown for E2E tests
 * Runs after all tests to clean up the test environment
 */

import { execSync } from 'child_process'

async function globalTeardown() {
  console.log('🧹 Cleaning up E2E test environment...')

  try {
    // Clean up test database
    console.log('🗄️ Cleaning up test database...')
    execSync('cd back && npx prisma db push --force-reset', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        DATABASE_URL: 'postgresql://test_user:test_password@localhost:5432/test_db?schema=public'
      }
    })

    console.log('✅ E2E test environment cleaned up!')
    
  } catch (error) {
    console.error('❌ Failed to cleanup E2E test environment:', error)
    // Don't throw error in teardown to avoid masking test failures
  }
}

export default globalTeardown
