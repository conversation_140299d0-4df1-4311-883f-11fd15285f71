/**
 * End-to-End Tests for Lexialia MMORPG
 * 
 * These tests verify the complete user journey from registration to gameplay.
 * Run with: npm run test:e2e
 */

import { test, expect } from '@playwright/test'

const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:4000'
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8001'

test.describe('Lexialia MMORPG E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the game
    await page.goto(FRONTEND_URL)
  })

  test('should load the game homepage', async ({ page }) => {
    // Check that the page loads
    await expect(page).toHaveTitle(/Lexialia/)
    
    // Check for key elements
    await expect(page.locator('text=Lexialia')).toBeVisible()
    await expect(page.locator('text=Login')).toBeVisible()
    await expect(page.locator('text=Sign Up')).toBeVisible()
  })

  test('should register a new user successfully', async ({ page }) => {
    const timestamp = Date.now()
    const testUser = {
      username: `testuser${timestamp}`,
      email: `test${timestamp}@example.com`,
      password: 'password123'
    }

    // Click sign up button
    await page.click('text=Sign Up')
    
    // Fill registration form
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    
    // Submit form
    await page.click('[data-testid="register-button"]')
    
    // Should redirect to game or show success
    await expect(page.locator('text=Welcome')).toBeVisible({ timeout: 10000 })
  })

  test('should login with existing user', async ({ page }) => {
    // First register a user
    const timestamp = Date.now()
    const testUser = {
      username: `logintest${timestamp}`,
      email: `logintest${timestamp}@example.com`,
      password: 'password123'
    }

    // Register via API first
    const response = await page.request.post(`${BACKEND_URL}/api/auth/register`, {
      data: testUser
    })
    expect(response.ok()).toBeTruthy()

    // Now test login
    await page.click('text=Login')
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.click('[data-testid="login-button"]')
    
    // Should show user profile or game interface
    await expect(page.locator(`text=${testUser.username}`)).toBeVisible({ timeout: 10000 })
  })

  test('should allow guest play without registration', async ({ page }) => {
    // Look for guest/play button
    await page.click('text=Play as Guest')
    
    // Should enter game world
    await expect(page.locator('canvas')).toBeVisible({ timeout: 15000 })
    await expect(page.locator('text=Guest')).toBeVisible()
  })

  test('should load 3D game world', async ({ page }) => {
    // Enter as guest
    await page.click('text=Play as Guest')
    
    // Wait for game to load
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Check that WebGL context is created
    const canvasExists = await page.evaluate(() => {
      const canvas = document.querySelector('canvas')
      return canvas && canvas.getContext('webgl2') !== null
    })
    expect(canvasExists).toBeTruthy()
  })

  test('should connect to WebSocket server', async ({ page }) => {
    // Monitor WebSocket connections
    let wsConnected = false
    page.on('websocket', ws => {
      wsConnected = true
      ws.on('framereceived', event => {
        console.log('WebSocket frame received:', event.payload)
      })
    })

    // Enter game
    await page.click('text=Play as Guest')
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Wait a bit for WebSocket connection
    await page.waitForTimeout(3000)
    
    expect(wsConnected).toBeTruthy()
  })

  test('should display chat interface', async ({ page }) => {
    // Enter game as guest
    await page.click('text=Play as Guest')
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Check for chat elements
    await expect(page.locator('[data-testid="chat-container"]')).toBeVisible()
    await expect(page.locator('[data-testid="chat-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="chat-messages"]')).toBeVisible()
  })

  test('should send and receive chat messages', async ({ page }) => {
    // Enter game as guest
    await page.click('text=Play as Guest')
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Send a chat message
    const testMessage = `Test message ${Date.now()}`
    await page.fill('[data-testid="chat-input"]', testMessage)
    await page.press('[data-testid="chat-input"]', 'Enter')
    
    // Check that message appears in chat
    await expect(page.locator(`text=${testMessage}`)).toBeVisible({ timeout: 5000 })
  })

  test('should show player progression UI for registered users', async ({ page }) => {
    // Register and login
    const timestamp = Date.now()
    const testUser = {
      username: `progtest${timestamp}`,
      email: `progtest${timestamp}@example.com`,
      password: 'password123'
    }

    await page.request.post(`${BACKEND_URL}/api/auth/register`, {
      data: testUser
    })

    await page.click('text=Login')
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.click('[data-testid="login-button"]')
    
    // Wait for game to load
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Check for progression UI elements
    await expect(page.locator('[data-testid="player-level"]')).toBeVisible()
    await expect(page.locator('[data-testid="xp-bar"]')).toBeVisible()
    await expect(page.locator('[data-testid="coin-count"]')).toBeVisible()
  })

  test('should handle server health checks', async ({ page }) => {
    // Test health endpoint
    const healthResponse = await page.request.get(`${BACKEND_URL}/api/health`)
    expect(healthResponse.ok()).toBeTruthy()
    
    const healthData = await healthResponse.json()
    expect(healthData.status).toBe('ok')
  })

  test('should handle authentication API endpoints', async ({ page }) => {
    const timestamp = Date.now()
    const testUser = {
      username: `apitest${timestamp}`,
      email: `apitest${timestamp}@example.com`,
      password: 'password123'
    }

    // Test registration endpoint
    const registerResponse = await page.request.post(`${BACKEND_URL}/api/auth/register`, {
      data: testUser
    })
    expect(registerResponse.ok()).toBeTruthy()
    
    const registerData = await registerResponse.json()
    expect(registerData.success).toBeTruthy()
    expect(registerData.user.username).toBe(testUser.username)
    expect(registerData.token).toBeDefined()

    // Test login endpoint
    const loginResponse = await page.request.post(`${BACKEND_URL}/api/auth/login`, {
      data: {
        username: testUser.username,
        password: testUser.password
      }
    })
    expect(loginResponse.ok()).toBeTruthy()
    
    const loginData = await loginResponse.json()
    expect(loginData.success).toBeTruthy()
    expect(loginData.token).toBeDefined()
  })

  test('should handle multiple concurrent users', async ({ browser }) => {
    // Create multiple browser contexts to simulate different users
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext()
    ])

    const pages = await Promise.all(
      contexts.map(context => context.newPage())
    )

    // Navigate all pages to the game
    await Promise.all(
      pages.map(page => page.goto(FRONTEND_URL))
    )

    // Enter game as guests
    await Promise.all(
      pages.map(page => page.click('text=Play as Guest'))
    )

    // Wait for all games to load
    await Promise.all(
      pages.map(page => page.waitForSelector('canvas', { timeout: 20000 }))
    )

    // Send messages from each user
    for (let i = 0; i < pages.length; i++) {
      const message = `Message from user ${i + 1} - ${Date.now()}`
      await pages[i].fill('[data-testid="chat-input"]', message)
      await pages[i].press('[data-testid="chat-input"]', 'Enter')
    }

    // Verify messages appear on all clients
    await Promise.all(
      pages.map(page => 
        expect(page.locator('text=Message from user')).toBeVisible({ timeout: 10000 })
      )
    )

    // Cleanup
    await Promise.all(contexts.map(context => context.close()))
  })

  test('should persist user data across sessions', async ({ page }) => {
    // Register user
    const timestamp = Date.now()
    const testUser = {
      username: `persisttest${timestamp}`,
      email: `persisttest${timestamp}@example.com`,
      password: 'password123'
    }

    await page.request.post(`${BACKEND_URL}/api/auth/register`, {
      data: testUser
    })

    // Login and play
    await page.click('text=Login')
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.click('[data-testid="login-button"]')
    
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Get initial level/XP
    const initialLevel = await page.textContent('[data-testid="player-level"]')
    
    // Refresh page (simulate session restart)
    await page.reload()
    
    // Login again
    await page.click('text=Login')
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.click('[data-testid="login-button"]')
    
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Verify data persisted
    const persistedLevel = await page.textContent('[data-testid="player-level"]')
    expect(persistedLevel).toBe(initialLevel)
  })
})

// Performance tests
test.describe('Performance Tests', () => {
  test('should load game within acceptable time', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto(FRONTEND_URL)
    await page.click('text=Play as Guest')
    await page.waitForSelector('canvas', { timeout: 30000 })
    
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(30000) // Should load within 30 seconds
  })

  test('should maintain stable FPS', async ({ page }) => {
    await page.goto(FRONTEND_URL)
    await page.click('text=Play as Guest')
    await page.waitForSelector('canvas', { timeout: 20000 })
    
    // Monitor FPS for a few seconds
    const fps = await page.evaluate(() => {
      return new Promise(resolve => {
        let frameCount = 0
        const startTime = performance.now()
        
        function countFrame() {
          frameCount++
          if (performance.now() - startTime < 3000) {
            requestAnimationFrame(countFrame)
          } else {
            const avgFPS = frameCount / 3
            resolve(avgFPS)
          }
        }
        
        requestAnimationFrame(countFrame)
      })
    })
    
    expect(fps).toBeGreaterThan(15) // Should maintain at least 15 FPS
  })
})
