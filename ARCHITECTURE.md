# Lexialia MMORPG Architecture Documentation

## Overview

Lexialia is a modern web-based MMORPG built with a full-stack TypeScript architecture. The game features real-time multiplayer gameplay, persistent world state, user authentication, progression systems, and modern UI/UX design.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (Next.js)     │◄──►│   (Node.js)     │◄──►│  (PostgreSQL)   │
│                 │    │                 │    │                 │
│ • React 19      │    │ • WebSockets    │    │ • User Data     │
│ • Three.js      │    │ • ECS System    │    │ • Game State    │
│ • Tailwind CSS  │    │ • Physics       │    │ • Chat History  │
│ • Auth Context  │    │ • Persistence   │    │ • Progression   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

#### Frontend
- **Framework**: Next.js 15 with React 19
- **3D Engine**: Three.js with React Three Fiber
- **Physics**: Rapier.js (client-side prediction)
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: shadcn/ui with Radix UI primitives
- **State Management**: React Context API
- **Authentication**: JWT tokens with HTTP-only cookies

#### Backend
- **Runtime**: Node.js with TypeScript
- **WebSocket Server**: uWebSockets.js (high-performance)
- **Game Architecture**: Entity Component System (ECS)
- **Physics**: Rapier.js (server-authoritative)
- **Database ORM**: Prisma
- **Authentication**: JWT with bcrypt password hashing
- **Serialization**: MessagePack for efficient networking

#### Database
- **Primary Database**: PostgreSQL 15
- **Schema Management**: Prisma migrations
- **Connection Pooling**: Built-in Prisma connection pooling

#### Infrastructure
- **Containerization**: Docker with docker-compose
- **Reverse Proxy**: Nginx Proxy Manager
- **CI/CD**: GitHub Actions
- **Monitoring**: Custom health checks and metrics

## Core Systems

### 1. Entity Component System (ECS)

The game uses a custom ECS architecture for managing game objects:

```typescript
// Entity: Unique identifier for game objects
class Entity {
  id: string
  type: SerializedEntityType
  components: Map<string, Component>
}

// Component: Data containers
class PositionComponent extends Component {
  x: number
  y: number
  z: number
}

// System: Logic processors
class MovementSystem {
  update(entities: Entity[]) {
    // Process movement logic
  }
}
```

### 2. Networking Architecture

#### WebSocket Communication
- **Protocol**: Binary WebSocket with MessagePack serialization
- **Message Types**: Input, Chat, Authentication, Game Events
- **Rate Limiting**: Per-user rate limiting for chat and actions
- **Compression**: Built-in message compression

#### Client-Server Synchronization
- **Server Authority**: All game state changes validated on server
- **Client Prediction**: Local input prediction with server reconciliation
- **Delta Compression**: Only changed components are synchronized
- **Tick Rate**: Configurable (default 20 TPS)

### 3. Authentication System

#### JWT-Based Authentication
```typescript
interface AuthUser {
  id: string
  username: string
  email: string
  profile?: UserProfile
}

interface UserProfile {
  displayName: string
  level: number
  experience: number
  coins: number
}
```

#### Security Features
- Password hashing with bcrypt
- JWT token expiration and refresh
- CORS protection
- Rate limiting on auth endpoints
- Input validation and sanitization

### 4. Persistence Layer

#### Database Schema
```sql
-- Core user data
User {
  id: String (UUID)
  username: String (unique)
  email: String (unique)
  passwordHash: String
  createdAt: DateTime
}

-- Player progression
UserProfile {
  userId: String (FK)
  displayName: String
  level: Int
  experience: Int
  coins: Int
  totalPlayTime: Int
}

-- Character state
Character {
  userId: String (FK)
  positionX: Float
  positionY: Float
  positionZ: Float
  rotationY: Float
  color: String
  size: Float
  currentRoom: String
  isOnline: Boolean
}

-- Chat system
ChatMessage {
  roomId: String
  userId: String (nullable)
  username: String
  content: String
  messageType: Enum
  createdAt: DateTime
}

-- Inventory system
InventoryItem {
  userId: String (FK)
  itemType: String
  itemName: String
  quantity: Int
  metadata: Json
}
```

### 5. Game Systems

#### Movement and Physics
- **Server-Authoritative**: All physics calculations on server
- **Client Prediction**: Smooth movement with lag compensation
- **Collision Detection**: Rapier.js physics engine
- **Ground Detection**: Raycasting for character grounding

#### Chat System
- **Persistent History**: Messages stored in database
- **Rate Limiting**: Anti-spam protection
- **Message Validation**: Content filtering and length limits
- **Real-time Broadcast**: Instant message delivery

#### Progression System
- **XP and Levels**: Exponential XP curve with level rewards
- **Action Rewards**: XP for various game activities
- **Coin Economy**: Virtual currency for purchases
- **Leaderboards**: Global player rankings

#### Inventory System
- **Item Definitions**: Configurable item types and properties
- **Stackable Items**: Quantity-based inventory management
- **Item Categories**: Tools, cosmetics, consumables, materials
- **Starter Items**: New player onboarding items

## Performance Optimizations

### Network Optimization
- **MessagePack Serialization**: 50% smaller than JSON
- **Delta Compression**: Only send changed data
- **Component Batching**: Group updates for efficiency
- **Connection Pooling**: Efficient database connections

### Memory Management
- **Entity Pooling**: Reuse entity objects
- **Component Cleanup**: Automatic garbage collection
- **Rate Limiting**: Prevent memory leaks from spam

### Database Optimization
- **Indexed Queries**: Optimized database indexes
- **Connection Pooling**: Prisma connection management
- **Batch Operations**: Bulk database updates
- **Cleanup Jobs**: Automatic old data removal

## Security Considerations

### Authentication Security
- **Password Hashing**: bcrypt with salt rounds
- **JWT Security**: Secure token generation and validation
- **Session Management**: Proper token expiration
- **CORS Protection**: Restricted cross-origin requests

### Input Validation
- **Server Validation**: All inputs validated server-side
- **Sanitization**: HTML/script injection prevention
- **Rate Limiting**: Prevent abuse and DoS attacks
- **Message Filtering**: Chat content moderation

### Data Protection
- **Environment Variables**: Sensitive data in env files
- **Database Security**: Parameterized queries (Prisma)
- **Error Handling**: No sensitive data in error messages
- **Logging**: Secure logging without credentials

## Deployment Architecture

### Docker Configuration
```yaml
services:
  postgres:      # Database server
  game_backend:  # Node.js game server
  game_frontend: # Next.js frontend
  nginx_proxy:   # Reverse proxy
  watchtower:    # Auto-updates
```

### CI/CD Pipeline
1. **Code Quality**: ESLint, TypeScript checks
2. **Testing**: Unit tests, integration tests
3. **Security**: Vulnerability scanning
4. **Build**: Docker image creation
5. **Deploy**: Automated deployment

### Monitoring
- **Health Checks**: `/api/health` endpoint
- **Metrics**: `/api/metrics` for monitoring
- **Logging**: Structured JSON logging
- **Performance**: Memory and CPU monitoring

## Development Workflow

### Local Development
```bash
# Start development environment
docker-compose up -d postgres
cd back && npm run dev
cd front && npm run dev
```

### Testing
```bash
# Backend tests
cd back && npm test

# Frontend tests
cd front && npm test

# E2E tests
npm run test:e2e
```

### Database Management
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Reset database
npx prisma migrate reset
```

## Future Enhancements

### Planned Features
- **Multiple Rooms**: Dynamic room switching
- **Voice Chat**: WebRTC voice communication
- **Mobile Support**: React Native mobile app
- **Advanced Physics**: More complex game mechanics
- **Scripting System**: Lua scripting for game modes

### Scalability Improvements
- **Horizontal Scaling**: Multiple game server instances
- **Load Balancing**: Distribute players across servers
- **Database Sharding**: Scale database horizontally
- **CDN Integration**: Static asset delivery
- **Caching Layer**: Redis for session management

## Contributing

### Code Standards
- **TypeScript**: Strict type checking
- **ESLint**: Code quality enforcement
- **Prettier**: Code formatting
- **Conventional Commits**: Standardized commit messages

### Architecture Principles
- **Separation of Concerns**: Clear system boundaries
- **Single Responsibility**: Each class has one purpose
- **Dependency Injection**: Loose coupling between systems
- **Event-Driven**: Decoupled system communication
- **Performance First**: Optimize for real-time gameplay
