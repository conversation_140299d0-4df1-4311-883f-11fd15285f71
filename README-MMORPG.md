# 🎮 Lexialia MMORPG - Enhanced Multiplayer Experience

**An advanced MMORPG transformation of the original Lexialia/NotBlox game engine, featuring persistent world state, user authentication, progression systems, and modern UI/UX design.**

> 🚀 **This is an enhanced version** of the original Three.js multiplayer game demo, now featuring full MMORPG capabilities including user accounts, character progression, inventory systems, and persistent world state.

## 🌟 What's New in the MMORPG Version

### 🔐 **Complete Authentication System**
- **User Registration & Login**: Secure JWT-based authentication
- **Character Persistence**: Your character and progress save across sessions
- **Guest Mode**: Play immediately without registration
- **Profile Management**: Customizable display names and user profiles

### 📈 **Progression & Economy**
- **XP & Leveling System**: Gain experience through gameplay activities
- **Coin Economy**: Earn and spend virtual currency
- **Daily Login Bonuses**: Rewards for regular play
- **Global Leaderboards**: Compete with other players

### 🎒 **Inventory & Items**
- **Item Management**: Collect tools, cosmetics, consumables, and materials
- **Starter Kit**: New players receive helpful starter items
- **Item Usage**: Consumables with real effects (XP boosts, coin pouches)
- **Stackable Items**: Efficient inventory management

### 💬 **Enhanced Chat System**
- **Persistent Chat History**: Messages saved and loaded across sessions
- **Rate Limiting**: Anti-spam protection
- **Message Validation**: Content filtering and length limits
- **Real-time Notifications**: System messages for progression events

### 🏗️ **Persistent World State**
- **Room Management**: Multiple persistent game worlds
- **World State Snapshots**: Server saves world state periodically
- **Character Positions**: Spawn where you last logged out
- **Database Integration**: PostgreSQL for reliable data storage

### 🎨 **Modern UI/UX Design**
- **Dark Theme**: Professional MMORPG-style interface
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Game HUD**: Player stats, XP bar, chat interface, and controls
- **Authentication Modals**: Smooth login/register experience
- **Animated Components**: Smooth transitions and micro-interactions

## 🚀 Quick Start

### Option 1: Docker (Recommended)
```bash
# Clone and start all services
git clone <repository-url>
cd lexialia-v2
docker-compose up -d

# Access the game
open http://localhost:4000
```

### Option 2: Development Setup
```bash
# Prerequisites: Node.js 18+, PostgreSQL
git clone <repository-url>
cd lexialia-v2

# Start database
docker-compose up -d postgres

# Backend setup
cd back
npm install
cp .env.example .env
npx prisma generate
npx prisma migrate dev
npm run dev

# Frontend setup (new terminal)
cd front
npm install
cp .env.example .env.local
npm run dev
```

## 🎯 Game Features

### Original Features (Enhanced)
- **Multiplayer**: Real-time multiplayer with up to 50+ players
- **3D Physics**: Rapier.js physics engine with collision detection
- **Server Authoritative**: Cheat-resistant server-side validation
- **ECS Architecture**: Scalable Entity Component System
- **Multiple Worlds**: Test World, Obby Parkour, Football, Pet Simulator
- **Vehicle System**: Driveable cars with realistic physics
- **Interaction System**: Proximity-based object interactions

### New MMORPG Features
- **Account System**: Secure user registration and authentication
- **Character Progression**: XP, levels, and skill advancement
- **Persistent Data**: All progress saved to PostgreSQL database
- **Inventory Management**: Collect and use various items
- **Chat Persistence**: Message history across sessions
- **Daily Rewards**: Login bonuses and activity rewards
- **Leaderboards**: Global player rankings
- **Health Monitoring**: System metrics and performance tracking

## 🛠️ Technical Architecture

### Enhanced Technology Stack
- **Frontend**: Next.js 15, React 19, Three.js, Tailwind CSS, shadcn/ui
- **Backend**: Node.js, TypeScript, uWebSockets.js, Prisma ORM
- **Database**: PostgreSQL 15 with connection pooling
- **Authentication**: JWT tokens with bcrypt password hashing
- **Real-time**: WebSocket with MessagePack serialization
- **Infrastructure**: Docker, GitHub Actions CI/CD, Nginx Proxy Manager

### Database Schema
```sql
-- User management
User { id, username, email, passwordHash, createdAt }
UserProfile { userId, displayName, level, experience, coins, totalPlayTime }

-- Character persistence  
Character { userId, positionX, positionY, positionZ, rotationY, color, size, currentRoom, isOnline }

-- Chat system
ChatMessage { roomId, userId, username, content, messageType, createdAt }

-- Inventory system
InventoryItem { userId, itemType, itemName, quantity, metadata }

-- World persistence
Room { name, displayName, mapUrl, maxPlayers, tickRate, gameScript }
RoomSnapshot { roomId, worldState, playerCount, createdAt }
```

## 📊 System Monitoring

### Health Endpoints
- **Basic Health**: `GET /api/health` - Simple health check
- **Detailed Status**: `GET /api/health/detailed` - Comprehensive system status
- **Metrics**: `GET /api/metrics` - Performance and usage metrics

### Monitoring Features
- Real-time player count tracking
- Database connection monitoring
- Memory usage and performance metrics
- Error logging and alerting
- Automated cleanup of old data

## 🧪 Testing & Quality Assurance

### Comprehensive Testing Suite
```bash
# Backend tests (authentication, progression, chat)
cd back && npm test

# Frontend tests (components, integration)
cd front && npm test

# E2E testing scenarios
npm run test:e2e
```

### Test Coverage
- **Unit Tests**: Authentication, progression, inventory systems
- **Integration Tests**: Database operations, API endpoints
- **Performance Tests**: Load testing with multiple concurrent users
- **Security Tests**: Input validation, SQL injection prevention

## 📚 Documentation

- **[ARCHITECTURE.md](ARCHITECTURE.md)**: Technical architecture and system design
- **[PLAYTEST-GUIDE.md](PLAYTEST-GUIDE.md)**: Comprehensive testing scenarios and QA procedures
- **[PERFORMANCE.md](PERFORMANCE.md)**: Performance optimization guide
- **API Documentation**: Available at `/api/docs` when running

## 🎮 Game Worlds

### Available Worlds
1. **Test World**: Main hub with basic gameplay mechanics
2. **Obby Parkour**: Challenging parkour course with obstacles
3. **Football**: Multiplayer football/soccer gameplay
4. **Pet Simulator**: Virtual pet collection and care

### World Features
- **Persistent State**: World changes are saved and restored
- **Player Capacity**: Each world supports different player limits
- **Custom Scripts**: Unique gameplay mechanics per world
- **Dynamic Loading**: Efficient 3D asset loading and management

## 🚀 Deployment & Production

### Production Features
- **Docker Deployment**: Complete containerized setup
- **CI/CD Pipeline**: Automated testing and deployment
- **Load Balancing**: Support for multiple server instances
- **Auto-scaling**: Watchtower for automatic updates
- **SSL/TLS**: Secure HTTPS connections
- **CDN Ready**: Optimized for content delivery networks

### Performance Optimizations
- **MessagePack Serialization**: 50% smaller than JSON
- **Delta Compression**: Only send changed data
- **Connection Pooling**: Efficient database connections
- **Asset Optimization**: Compressed 3D models and textures
- **Caching**: Redis-ready for session management

## 🤝 Contributing

### Development Workflow
1. **Fork** the repository
2. **Create** feature branch (`git checkout -b feature/amazing-feature`)
3. **Follow** code standards (TypeScript, ESLint, Prettier)
4. **Write** tests for new features
5. **Submit** pull request with detailed description

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Code quality enforcement
- **Prettier**: Consistent formatting
- **Conventional Commits**: Standardized commit messages
- **Test Coverage**: Minimum 80% coverage for new features

## 📈 Roadmap

### Planned Features
- **Mobile App**: React Native mobile client
- **Voice Chat**: WebRTC voice communication
- **Guild System**: Player organizations and group activities
- **Crafting System**: Item creation and resource management
- **PvP Arenas**: Competitive player vs player combat
- **Quest System**: Structured missions and objectives
- **Marketplace**: Player-to-player item trading

### Technical Improvements
- **Horizontal Scaling**: Multiple server instances
- **Advanced Physics**: More complex game mechanics
- **AI NPCs**: Intelligent non-player characters
- **Scripting API**: Lua scripting for custom game modes
- **Analytics**: Player behavior tracking and insights

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Original Lexialia/NotBlox**: Foundation for this MMORPG enhancement
- **Three.js Community**: Amazing 3D engine and ecosystem
- **Rapier.js**: Excellent physics simulation
- **Prisma**: Outstanding database toolkit
- **Next.js Team**: Fantastic React framework
- **uWebSockets.js**: High-performance WebSocket implementation

---

**🎮 From simple multiplayer demo to full MMORPG experience!**

*This enhanced version transforms the original Three.js multiplayer game into a complete MMORPG with persistent progression, modern UI/UX, and enterprise-grade architecture.*
