version: '3.8'

services:
  # PostgreSQL Database for MMORPG persistence
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: lexialia_db
      POSTGRES_USER: lexialia
      POSTGRES_PASSWORD: lexialia_password
    ports:
      - '5434:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lexialia -d lexialia_db"]
      interval: 30s
      timeout: 10s
      retries: 3

# Persistent volumes
volumes:
  postgres_data:
    driver: local
