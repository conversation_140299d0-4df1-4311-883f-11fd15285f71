name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: |
          back/package-lock.json
          front/package-lock.json

    # Backend Tests
    - name: Install backend dependencies
      working-directory: ./back
      run: npm ci

    - name: Setup test database
      working-directory: ./back
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db?schema=public
      run: |
        npx prisma generate
        npx prisma db push

    - name: Run backend tests
      working-directory: ./back
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db?schema=public
        JWT_SECRET: test-secret-key
        NODE_ENV: test
      run: npm test

    - name: Run backend linting
      working-directory: ./back
      run: npm run lint || true

    # Frontend Tests
    - name: Install frontend dependencies
      working-directory: ./front
      run: npm ci

    - name: Run frontend tests
      working-directory: ./front
      run: npm test || true

    - name: Run frontend linting
      working-directory: ./front
      run: npm run lint || true

    - name: Build frontend
      working-directory: ./front
      env:
        NEXT_PUBLIC_SERVER_URL: ws://localhost:8001
      run: npm run build

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        component: [backend, frontend]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.component == 'backend' && 'back' || 'front' }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deployment step would go here"
        echo "This could trigger a webhook to your server to pull the latest images"
        echo "Or use a deployment service like Portainer, Watchtower, etc."

  security-scan:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  performance-test:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install dependencies
      working-directory: ./back
      run: npm ci

    - name: Setup database
      working-directory: ./back
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db?schema=public
      run: |
        npx prisma generate
        npx prisma db push

    - name: Start game server
      working-directory: ./back
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db?schema=public
        JWT_SECRET: test-secret-key
        NODE_ENV: test
      run: |
        npm start &
        sleep 10

    - name: Run performance tests
      run: |
        echo "Performance tests would go here"
        echo "Could use tools like Artillery, k6, or custom WebSocket load tests"
        echo "Testing concurrent connections, message throughput, etc."

  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install dependencies
      run: |
        cd back && npm ci
        cd ../front && npm ci

    - name: Run code quality checks
      run: |
        echo "Code quality checks would go here"
        echo "Could include ESLint, Prettier, TypeScript checks, etc."
        cd back && npm run lint || true
        cd ../front && npm run lint || true

    - name: Check for security vulnerabilities
      run: |
        cd back && npm audit --audit-level=high || true
        cd ../front && npm audit --audit-level=high || true
