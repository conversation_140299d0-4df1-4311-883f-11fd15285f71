
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/wasm-engine-edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.16.2
 * Query Engine version: 1c57fdcd7e44b29b9313256c76699e91c3ac3c43
 */
Prisma.prismaVersion = {
  client: "6.16.2",
  engine: "1c57fdcd7e44b29b9313256c76699e91c3ac3c43"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  password: 'password',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  displayName: 'displayName',
  avatar: 'avatar',
  bio: 'bio',
  level: 'level',
  experience: 'experience',
  coins: 'coins',
  totalPlayTime: 'totalPlayTime',
  gamesPlayed: 'gamesPlayed',
  lastActiveAt: 'lastActiveAt'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  token: 'token',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.CharacterScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  positionX: 'positionX',
  positionY: 'positionY',
  positionZ: 'positionZ',
  rotationY: 'rotationY',
  color: 'color',
  size: 'size',
  currentRoom: 'currentRoom',
  isOnline: 'isOnline',
  lastSeen: 'lastSeen'
};

exports.Prisma.RoomScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  mapUrl: 'mapUrl',
  maxPlayers: 'maxPlayers',
  tickRate: 'tickRate',
  gameScript: 'gameScript',
  worldState: 'worldState',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoomSnapshotScalarFieldEnum = {
  id: 'id',
  roomId: 'roomId',
  worldState: 'worldState',
  playerCount: 'playerCount',
  createdAt: 'createdAt'
};

exports.Prisma.ChatMessageScalarFieldEnum = {
  id: 'id',
  roomId: 'roomId',
  userId: 'userId',
  username: 'username',
  content: 'content',
  messageType: 'messageType',
  createdAt: 'createdAt'
};

exports.Prisma.InventoryItemScalarFieldEnum = {
  id: 'id',
  characterId: 'characterId',
  itemType: 'itemType',
  itemId: 'itemId',
  quantity: 'quantity',
  metadata: 'metadata'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  User: 'User',
  UserProfile: 'UserProfile',
  UserSession: 'UserSession',
  Character: 'Character',
  Room: 'Room',
  RoomSnapshot: 'RoomSnapshot',
  ChatMessage: 'ChatMessage',
  InventoryItem: 'InventoryItem'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "/var/www/html/lexialia-v2 (autre copie)/generated/prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "debian-openssl-3.0.x",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "/var/www/html/lexialia-v2 (autre copie)/back/prisma/schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../back/.env"
  },
  "relativePath": "../../back/prisma",
  "clientVersion": "6.16.2",
  "engineVersion": "1c57fdcd7e44b29b9313256c76699e91c3ac3c43",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// Lexialia MMORPG Database Schema\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\ngenerator client {\n  provider = \"prisma-client-js\"\n  output   = \"../../generated/prisma\"\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n}\n\n// User authentication and account management\nmodel User {\n  id        String   @id @default(cuid())\n  email     String   @unique\n  username  String   @unique\n  password  String // bcrypt hashed\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // User profile and game data\n  profile    UserProfile?\n  characters Character[]\n  sessions   UserSession[]\n\n  @@map(\"users\")\n}\n\n// Extended user profile information\nmodel UserProfile {\n  id          String  @id @default(cuid())\n  userId      String  @unique\n  displayName String?\n  avatar      String? // URL to avatar image\n  bio         String?\n  level       Int     @default(1)\n  experience  Int     @default(0)\n  coins       Int     @default(100)\n\n  // Game statistics\n  totalPlayTime Int      @default(0) // in seconds\n  gamesPlayed   Int      @default(0)\n  lastActiveAt  DateTime @default(now())\n\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"user_profiles\")\n}\n\n// User session management for JWT tokens\nmodel UserSession {\n  id        String   @id @default(cuid())\n  userId    String\n  token     String   @unique\n  expiresAt DateTime\n  createdAt DateTime @default(now())\n\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"user_sessions\")\n}\n\n// Character/Player data for in-game representation\nmodel Character {\n  id     String @id @default(cuid())\n  userId String\n  name   String\n\n  // Position and appearance\n  positionX Float @default(0)\n  positionY Float @default(5)\n  positionZ Float @default(0)\n  rotationY Float @default(0)\n\n  // Character customization\n  color String @default(\"#3b82f6\")\n  size  Float  @default(1.5)\n\n  // Game state\n  currentRoom String   @default(\"world-1\")\n  isOnline    Boolean  @default(false)\n  lastSeen    DateTime @default(now())\n\n  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)\n  inventoryItems InventoryItem[]\n\n  @@map(\"characters\")\n}\n\n// Persistent room/world state\nmodel Room {\n  id          String @id @default(cuid())\n  name        String @unique\n  displayName String\n  mapUrl      String\n  maxPlayers  Int    @default(50)\n\n  // Room configuration\n  tickRate   Int    @default(20)\n  gameScript String @default(\"defaultScript.js\")\n\n  // Persistence\n  worldState Json? // Serialized world state\n  createdAt  DateTime @default(now())\n  updatedAt  DateTime @updatedAt\n\n  snapshots RoomSnapshot[]\n\n  @@map(\"rooms\")\n}\n\n// Room state snapshots for persistence\nmodel RoomSnapshot {\n  id          String   @id @default(cuid())\n  roomId      String\n  worldState  Json // Serialized ECS state\n  playerCount Int      @default(0)\n  createdAt   DateTime @default(now())\n\n  room Room @relation(fields: [roomId], references: [id], onDelete: Cascade)\n\n  @@map(\"room_snapshots\")\n}\n\n// Chat message history\nmodel ChatMessage {\n  id          String   @id @default(cuid())\n  roomId      String\n  userId      String? // null for system messages\n  username    String\n  content     String\n  messageType String   @default(\"user\") // user, system, admin\n  createdAt   DateTime @default(now())\n\n  @@map(\"chat_messages\")\n}\n\n// Basic inventory system\nmodel InventoryItem {\n  id          String @id @default(cuid())\n  characterId String\n  itemType    String // \"weapon\", \"tool\", \"consumable\", etc.\n  itemId      String // identifier for the item\n  quantity    Int    @default(1)\n  metadata    Json? // additional item properties\n\n  character Character @relation(fields: [characterId], references: [id], onDelete: Cascade)\n\n  @@map(\"inventory_items\")\n}\n",
  "inlineSchemaHash": "336dee08bdf971de87244b9aba84dc7a958e0d9aedacd4ec6de5f140579c369c",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"username\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"password\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"profile\",\"kind\":\"object\",\"type\":\"UserProfile\",\"relationName\":\"UserToUserProfile\"},{\"name\":\"characters\",\"kind\":\"object\",\"type\":\"Character\",\"relationName\":\"CharacterToUser\"},{\"name\":\"sessions\",\"kind\":\"object\",\"type\":\"UserSession\",\"relationName\":\"UserToUserSession\"}],\"dbName\":\"users\"},\"UserProfile\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"displayName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"avatar\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"bio\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"level\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"experience\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"coins\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalPlayTime\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"gamesPlayed\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"lastActiveAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserProfile\"}],\"dbName\":\"user_profiles\"},\"UserSession\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserSession\"}],\"dbName\":\"user_sessions\"},\"Character\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"positionX\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"positionY\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"positionZ\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"rotationY\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"color\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"size\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"currentRoom\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isOnline\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"lastSeen\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"CharacterToUser\"},{\"name\":\"inventoryItems\",\"kind\":\"object\",\"type\":\"InventoryItem\",\"relationName\":\"CharacterToInventoryItem\"}],\"dbName\":\"characters\"},\"Room\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"displayName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"mapUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"maxPlayers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"tickRate\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"gameScript\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"worldState\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"snapshots\",\"kind\":\"object\",\"type\":\"RoomSnapshot\",\"relationName\":\"RoomToRoomSnapshot\"}],\"dbName\":\"rooms\"},\"RoomSnapshot\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"roomId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"worldState\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"playerCount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"room\",\"kind\":\"object\",\"type\":\"Room\",\"relationName\":\"RoomToRoomSnapshot\"}],\"dbName\":\"room_snapshots\"},\"ChatMessage\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"roomId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"username\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"content\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"messageType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":\"chat_messages\"},\"InventoryItem\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"characterId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"itemType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"itemId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"quantity\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"character\",\"kind\":\"object\",\"type\":\"Character\",\"relationName\":\"CharacterToInventoryItem\"}],\"dbName\":\"inventory_items\"}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = {
  getRuntime: async () => require('./query_engine_bg.js'),
  getQueryEngineWasmModule: async () => {
    const loader = (await import('#wasm-engine-loader')).default
    const engine = (await loader).default
    return engine
  }
}
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

